import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { UpdateNotificationDto } from './dto/update-notification.dto';
import { Notification } from './entities/notification.entity';

@Injectable()
export class NotificationService {
  create(createNotificationDto: CreateNotificationDto) {
    const notification = Notification.create({
      ...createNotificationDto,
      user: {
        id: createNotificationDto.userId,
      },
    });

    return Notification.save(notification);
  }

  readed(id: number) {
    return Notification.update(id, { readed: true });
  }

  getNotification(userId: number) {
    return Notification.find({
      where: {
        user: {
          id: userId,
        },
      },
      order: {
        createdAt: 'DESC',
      },
    });
  }

  // findAll() {
  //   return `This action returns all notification`;
  // }

  async findOne(id: number) {
    const notification = await Notification.findOneBy({ id });

    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    return notification;
  }

  // update(id: number, updateNotificationDto: UpdateNotificationDto) {
  //   return `This action updates a #${id} notification`;
  // }

  async remove(id: number) {
    const notification = await Notification.findOneBy({ id });

    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    return Notification.softRemove(notification);
  }
}
