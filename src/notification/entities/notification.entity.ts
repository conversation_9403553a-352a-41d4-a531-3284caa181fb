import { CustomBaseEntity } from "../../common/entities";
import { User } from "../../user/entities/user.entity";
import { Column, Entity, ManyToOne } from "typeorm";

@Entity()
export class Notification extends CustomBaseEntity {
  @Column()
  topic: string;

  @Column({ type: 'text' })
  detail: string;

  @Column({ default: false })
  readed: boolean;

  @Column({ nullable: true })
  targetId: number;

  @Column({ nullable: true })
  targetType: string;

  @ManyToOne(() => User, (user) => user.notifications)
  user: User;
}
