import { Body, Controller, Delete, Get, Param, Patch, Post, Put, Req } from '@nestjs/common';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { NotificationService } from './notification.service';
import { Request } from 'express';
import { NotificationGateway } from './notification.gateway';

@Controller('notification')
@Auth()
export class NotificationController {
  constructor(
    private readonly notificationService: NotificationService,
    private readonly notificationGateway: NotificationGateway,
  ) { }

  // @Post('test')
  // test(@Body() body: { topic: string, message: any }) {
  //   return this.notificationGateway.test(body.topic, body.message);
  // }

  @Put('/:id/readed')
  readed(@Param('id') id: string) {
    return this.notificationService.readed(+id);
  }

  @Get(':id')
  getNotification(@Param('id') id: string) {
    return this.notificationService.getNotification(+id);
  }


  @Get('')
  getByUser(@Req() req: Request) {
    const userId = req.user['sub'];

    return this.notificationService.getNotification(+userId);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.notificationService.remove(+id);
  }
}
