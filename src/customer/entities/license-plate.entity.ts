import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany, OneToOne } from "typeorm";
import { Upload } from "../../upload/entities/upload.entity";
import { Customer } from "./customer.entity";
import { Order } from "../../order/entities/order.entity";

@Entity()
export class LicensePlate extends CustomBaseEntity {
    @Column({ name: 'license_plate' })
    licensePlate: string;

    // @ManyToOne(() => Customer, (_) => _.licensePlates)
    // @JoinColumn({ name: 'customer_id' })
    // customer: Customer;

    // @OneToMany(() => Order, (_) => _.licensePlate)
    // orders: Order[];

    constructor(partial?: Partial<LicensePlate>) {
        super();
        if (partial) {
            Object.assign(this, partial)
        }
    }
}
