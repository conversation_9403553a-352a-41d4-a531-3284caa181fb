import { BadRequestException, Body, ClassSerializerInterceptor, Controller, Delete, Get, Header, HttpCode, HttpStatus, Param, ParseIntPipe, Post, Put, Query, Res, StreamableFile, UploadedFile, UseInterceptors } from '@nestjs/common';
import { CUSTOMER_PAGINATION_CONFIG, CustomerService } from './customer.service';
import { CreateCustomerBankDto, CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiBody, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { CreateLicensePlateDto } from './dto/create-license-plate.dto';
import { Readable } from 'stream';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('customer')
@ApiTags('สมาชิก')
@Auth()
@UseInterceptors(ClassSerializerInterceptor)
export class CustomerController {
  constructor(private readonly customerService: CustomerService) { }

  // @Post(':id/license-plate')
  // addLicensePlate(@Param('id') id: string, @Body() createLicensePlateDto: CreateLicensePlateDto) {
  //   return this.customerService.addLicensePlate(+id, createLicensePlateDto)
  // }
  @Post('import')
  @ApiOperation({
    summary: `นำเข้าข้อมูลลูกค้า`,
    description: `<h1>/uploads/static/import/customer-template.xlsx</h1>`
  })
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary'
        },
      },
    },
  })
  async importEmployeev2(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    return this.customerService.importExcel(file);
  }

  @Post(':id/bank')
  addBank(@Param('id') id: string, @Body() payload: CreateCustomerBankDto) {
    return this.customerService.addBank(+id, payload)
  }

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(CUSTOMER_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.customerService.datatables(query);
  }

  @Post()
  create(@Body() createCustomerDto: CreateCustomerDto) {
    return this.customerService.create(createCustomerDto);
  }

  @Get()
  findAll() {
    return this.customerService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.customerService.findOne(+id);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateCustomerDto: UpdateCustomerDto) {
    return this.customerService.update(+id, updateCustomerDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.customerService.remove(+id);
  }

  @Post('exports')
  @Header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
  @Header('Content-Disposition', 'attachment; filename="export-customer.xlsx"')
  async exportProducts() {
    const content: any = await this.customerService.exportAll();

    const file = Readable.from(content);

    // res.set({
    //   'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    //   'Content-Disposition': `attachment; filename="report-order.xlsx"`,
    // });

    return new StreamableFile(file);
  }
}
