import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateCustomerBankDto, CreateCustomerDto, CreateCustomerPlateDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
import { Customer } from './entities/customer.entity';
import { DataSource, QueryRunner, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';
// import { CreateLicensePlateDto } from './dto/create-license-plate.dto';
// import { LicensePlate } from './entities/license-plate.entity';
import { CustomerBank } from './entities/customer-bank.entity';
import { Workbook } from 'exceljs';
import * as xlsx from 'xlsx'

export const CUSTOMER_PAGINATION_CONFIG: PaginateConfig<Customer> = {
  sortableColumns: ['id', 'code', 'name', 'phoneNumber'],
  // select: ['id', 'code', 'name', 'phoneNumber', 'licensePlate', 'address', 'tax', 'createdAt'],
  searchableColumns: ['code', 'name', 'phoneNumber', 'tax', 'licensePlates', 'customerBanks.accountName', 'customerBanks.accountNumber'],
  filterableColumns: {
    // 'level.id': true,
  },
  defaultSortBy: [['code', 'ASC']],
  relations: {
    // level: true,
    customerBanks: true,
  }
};

@Injectable()
export class CustomerService {
  constructor(
    private dataSource: DataSource,
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
    // @InjectRepository(LicensePlate)
    // private licensePlateRepository: Repository<LicensePlate>,
    @InjectRepository(CustomerBank)
    private customerBankRepository: Repository<CustomerBank>,
  ) { }

  // addLicensePlate(id: number, createLicensePlateDto: CreateLicensePlateDto) {
  //   const lp = this.licensePlateRepository.create({
  //     ...createLicensePlateDto,
  //     customer: {
  //       id: id,
  //     }
  //   })

  //   return this.licensePlateRepository.save(lp);
  // }

  addBank(customerId: number, payload: CreateCustomerBankDto) {
    const { id, ...data } = payload
    const customerBank = CustomerBank.create({
      ...data,
      customer: {
        id: customerId,
      }
    })

    return customerBank.save();
  }

  async create(createCustomerDto: CreateCustomerDto) {
    const { identityCardId, ...createData } = createCustomerDto;

    //#check name or idcard exist
    const existName = await Customer.findOne({
      where:
        { name: createCustomerDto.name },
    })
    if (existName) {
      throw new BadRequestException('มีชื่อนี้ในระบบแล้ว')
    }

    if (createCustomerDto.tax != '0') {
      const existTax = await Customer.findOne({
        where:
          { tax: createCustomerDto.tax },
      })
      if (existTax) {
        throw new BadRequestException('มีเลขบัตรประชาชนในระบบแล้ว')
      }
    }

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const customer = this.customerRepository.create({
        ...createData,
        code: await this.genCusCode(),
        identityCard: {
          id: identityCardId,
        },
      });

      await queryRunner.manager.save(customer);

      // const licensePlates = [];
      // for (const licensePlate of createCustomerDto.licensePlates) {
      //   const lp = this.licensePlateRepository.create({
      //     customer,
      //     licensePlate: licensePlate.licensePlate,
      //   })

      //   licensePlates.push(lp)
      // }

      // await queryRunner.manager.save(licensePlates);

      const banks = [];
      for (const bank of createCustomerDto.banks) {
        const b = this.customerBankRepository.create({
          customer,
          accountName: bank.accountName,
          accountNumber: bank.accountNumber,
          bank: bank.bank,
        })

        banks.push(b)
      }
      await queryRunner.manager.save(banks);

      await queryRunner.commitTransaction();

      return this.findOne(customer.id);
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.log(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }

  }

  findAll() {
    return this.customerRepository.find({
      relations: {
        // licensePlates: true,
        // prefix: true
      },
      order: {
        code: 'ASC',
      }
    });
  }

  async findOne(id: number) {
    const customer = await this.customerRepository.findOne({
      where: { id },
      relations: {
        identityCard: true,
        // level: true,
        // licensePlates: true,
        // prefix: true,
        customerBanks: true,
      }
    });

    if (!customer) throw new NotFoundException("customer not found");

    return customer;
  }

  findById(id: number) {
    return this.customerRepository.findOne({
      where: { id },
      relations: {
        identityCard: true,
        // level: true,
        // licensePlates: true,
        // prefix: true,
        customerBanks: true
      }
    });
  }

  async update(id: number, updateCustomerDto: UpdateCustomerDto) {
    const customer = await this.findById(id);

    if (!customer) throw new NotFoundException("customer not found");

    const { identityCardId, banks, licensePlates, ...updateData } = updateCustomerDto;

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      const customerRepository = queryRunner.manager.getRepository(Customer);

      await customerRepository.update(id, {
        ...updateData,
        identityCard: { id: identityCardId ?? null },
        // level: { id: levelId ?? null },
        // prefix: { id: prefixId ?? null },
        licensePlates: licensePlates ?? null,
      });

      // await this.updateCustomerPlate(queryRunner, id, licensePlates)

      await this.updateCustomerBank(queryRunner, id, banks)

      await queryRunner.commitTransaction();

      return this.findOne(customer.id);
    } catch (err) {
      // since we have errors lets rollback the changes we made
      await queryRunner.rollbackTransaction();

      console.error(err)

      throw new BadRequestException(err?.message);
    } finally {
      // you need to release a queryRunner which was manually instantiated
      await queryRunner.release();
    }
  }

  async remove(id: number) {
    const customer = await this.findById(id);

    if (!customer) throw new NotFoundException("customer not found");

    await this.customerRepository.softRemove(customer);
  }

  async datatables(query: PaginateQuery): Promise<Paginated<Customer>> {
    return paginate(query, this.customerRepository, CUSTOMER_PAGINATION_CONFIG);
  }

  // async updateCustomerPlate(queryRunner: QueryRunner, customerId: number, data: CreateCustomerPlateDto[]) {
  //   const customerPlateRepository = queryRunner.manager.getRepository(LicensePlate);

  //   // const licensePlates = await customerPlateRepository.find({ where: { customer: { id: customerId } } })
  //   // const currentPlateIds = licensePlates.map(e => e.id)
  //   const newPlateIds = []

  //   for (let index = 0; index < data.length; index++) {
  //     const newData = data[index];

  //     if (newData.id == null) {
  //       //new
  //       const result = await customerPlateRepository.save({ customer: { id: customerId }, licensePlate: newData.licensePlate })
  //       newPlateIds.push(result.id)
  //     } else {
  //       //update
  //       await customerPlateRepository.update(newData.id, { licensePlate: newData.licensePlate })
  //       newPlateIds.push(newData.id)
  //     }
  //   }

  //   //delete row
  //   const deleteIds = currentPlateIds.filter(e => !newPlateIds.includes(e));
  //   await customerPlateRepository.remove(deleteIds.map(e => new LicensePlate({ id: e })))
  // }

  async updateCustomerBank(queryRunner: QueryRunner, customerId: number, data: CreateCustomerBankDto[]) {
    const customerBankRepository = queryRunner.manager.getRepository(CustomerBank);

    const customerBanks = await customerBankRepository.find({ where: { customer: { id: customerId } } })
    const currentBankIds = customerBanks.map(e => e.id)
    const newBankIds = []

    for (let index = 0; index < data.length; index++) {
      const newData = data[index];

      if (newData.id == null) {
        //new
        const result = await customerBankRepository.save({
          customer: { id: customerId },
          accountName: newData.accountName,
          accountNumber: newData.accountNumber,
          bank: newData.bank,
        })
        newBankIds.push(result.id)
      } else {
        //update
        await customerBankRepository.update(newData.id, {
          accountName: newData.accountName,
          accountNumber: newData.accountNumber,
          bank: newData.bank,
        })
        newBankIds.push(newData.id)
      }
    }

    //delete row
    const deleteIds = currentBankIds.filter(e => !newBankIds.includes(e));
    await customerBankRepository.remove(deleteIds.map(e => new CustomerBank({ id: e })))
  }

  async runNumber() {
    const last = await this.customerRepository.find({
      order: {
        code: 'DESC'
      },
      take: 1
    })

    if (last.length) {

    } else {

    }
  }

  async exportAll() {
    const workbook = new Workbook

    const worksheet = workbook.addWorksheet('customer')

    worksheet.columns = [
      { header: 'ลำดับ', key: 'no' },
      { header: 'รหัสสินค้า', key: 'code' },
      { header: 'ชื่อ', key: 'name' },
      { header: 'ที่อยู่', key: 'address' },
      { header: 'เบอร์โทร', key: 'phone_number' },
      { header: 'เลขบัตร', key: 'tax' },
    ]

    const content = await this.findAll()

    let data = content.map((e, i, _) => ({
      no: i + 1,
      code: e.code,
      name: e.name,
      address: e?.address ?? '',
      phone_number: e.phoneNumber ?? '',
      tax: e.tax,
    }));

    data.forEach((val, i, _) => {
      worksheet.addRow(val)
    })

    return workbook.xlsx.writeBuffer();
  }

  private async genCusCode(): Promise<string> {
    const lastCustomer = await Customer.find({
      select: ['id', 'code'], // Only fetch the `code` field
      order: {
        id: 'DESC'
      },
      take: 1
    });

    let code = 1;

    if (lastCustomer.length == 0) {
      // No customers exist yet
      return code.toString().padStart(5, '0');
    }

    const lastCode = parseInt(lastCustomer[0].code, 10);
    if (isNaN(lastCode)) {
      // Handle corrupted code gracefully
      throw new Error('Invalid customer code found in the database');
    }

    let newCode = (lastCode + 1).toString().padStart(5, '0');
    let isUnique = false;

    while (!isUnique) {
      const duplicate = await Customer.findOne({
        where: { code: newCode }
      });

      if (!duplicate) {
        isUnique = true;
      } else {
        newCode = (parseInt(newCode, 10) + 1).toString().padStart(5, '0');
      }
    }

    return newCode;
  }

  async importExcel(file: Express.Multer.File) {
    const HEADERS = [
      'รหัสลูกค้า (จำเป็น)', 'ชื่อลูกค้า (จำเป็น)', 'บริษัท', 'หมายเลขโทรศัพท์', 'ที่อยู่',
      'เลขประจำตัวผู้เสียภาษี',
    ];

    const workbook = xlsx.read(file.buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];
    const jsonData = xlsx.utils.sheet_to_json(sheet, { header: 1, defval: '' });

    const actualHeaders: string[] = jsonData[0] as Array<string>;

    // Validate headers
    const isValid = HEADERS.every(header => actualHeaders.includes(header));
    if (!isValid) {
      throw new BadRequestException('Header validation failed.');
    }

    const result = {
      ok: 0,
      error: 0,
      errorDetails: []
    };

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      for (let rowIndex = 1; rowIndex < jsonData.length; rowIndex++) {
        const row = jsonData[rowIndex];

        try {
          const code = row[actualHeaders.indexOf('รหัสลูกค้า (จำเป็น)')];
          const name = row[actualHeaders.indexOf('ชื่อลูกค้า (จำเป็น)')];
          const address = row[actualHeaders.indexOf('ที่อยู่')];
          const tax = row[actualHeaders.indexOf('เลขประจำตัวผู้เสียภาษี')];
          const company = row[actualHeaders.indexOf('บริษัท')];
          const phoneNumber = row[actualHeaders.indexOf('หมายเลขโทรศัพท์')];

          const existingCustomer = await queryRunner.manager.findOneBy(Customer, { code });
          if (existingCustomer) {
            throw new Error('code already exists');
          }

          const customer = queryRunner.manager.create(Customer, {
            code,
            name,
            address,
            tax,
            company,
            phoneNumber,
          });

          await queryRunner.manager.save(customer);

          result.ok += 1;
        } catch (error) {
          console.error(error);
          result.error += 1;
          result.errorDetails.push({
            row: rowIndex + 1,
            error: `on row ${rowIndex + 1}, ${error.message}`
          });
        }
      }

      await queryRunner.commitTransaction();
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw new BadRequestException(err?.message);
    } finally {
      await queryRunner.release();
      return result;
    }
  }
}
