import { Modu<PERSON> } from '@nestjs/common';
import { UploadController } from './upload.controller';
import { UploadService } from './upload.service';
import { MulterModule } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { mkdirSync } from 'fs-extra';
import { generate } from 'randomstring';
import { extname } from 'path';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Upload } from './entities/upload.entity';
import { DateTime } from 'luxon';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Upload
    ]),
    MulterModule.register({
      storage: diskStorage({
        destination: "./uploads",
        filename: (req, file, cb) => {
          const nowFormat = DateTime.now().toFormat('yyyy/MM/dd');
          const directory = nowFormat;
          mkdirSync('uploads/' + directory, {
            recursive: true,
          });

          const extension = extname(file.originalname);
          const fileName = generate() + extension;

          cb(null, `${directory}/${fileName}`)
        }
      })
    })
  ],
  controllers: [UploadController],
  providers: [UploadService]
})
export class UploadModule { }
