import { Column, <PERSON><PERSON>ty, ManyToOne, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { Product } from "../../product/entities/product.entity";
import { GoodsReceipt } from "./goods-receipt.entity";
import { PurchaseOrderItem } from "../../purchase-order/entities/purchase-order-item.entity";
import { PaymentVoucherItem } from "../../payment-voucher/entities";

@Entity()
export class GoodsReceiptItem extends CustomBaseEntity {
    @ManyToOne(() => GoodsReceipt, (gr) => gr.items, { onDelete: 'CASCADE' })
    goodsReceipt: GoodsReceipt;

    @ManyToOne(() => Product, (product) => product.goodsReceiptItems)
    product: Product;

    // @ManyToOne(() => PurchaseOrderItem, (poItem) => poItem.goodsReceiptItems, { nullable: true })
    // purchaseOrderItem: PurchaseOrderItem;

    // @Column({ nullable: true })
    // purchaseOrderItemId: number;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    receivedQuantity: number; // จำนวนที่รับจริง

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    usedQuantity: number; // จำนวนที่ใช้แล้ว

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    unitPrice: number; // ราคาต่อหน่วย

    @Column({ nullable: true })
    unitName: string;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    totalPrice: number; // ราคารวม

    @Column({ type: 'text', nullable: true })
    notes: string; // หมายเหตุ

    // @OneToMany(() => PaymentVoucherItem, (pvItem) => pvItem.goodsReceiptItem)
    // paymentVoucherItems: PaymentVoucherItem[];

    constructor(partial?: Partial<GoodsReceiptItem>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }
}
