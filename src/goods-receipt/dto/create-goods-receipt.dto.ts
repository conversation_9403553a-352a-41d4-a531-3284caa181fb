import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsDateString, IsArray, ValidateNested, IsEnum, IsBoolean, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateGoodsReceiptItemDto } from './create-goods-receipt-item.dto';
import { GoodsReceiptStatus } from '../entities/goods-receipt.entity';

export class CreateGoodsReceiptDto {
    @ApiProperty({ description: 'เลขที่ใบรับสินค้า' })
    @IsString()
    @IsNotEmpty()
    grNumber: string;

    @ApiProperty({ description: 'วันที่รับสินค้า' })
    @IsDateString()
    grDate: string;

    @ApiProperty({ description: 'สถานะ', enum: GoodsReceiptStatus, default: GoodsReceiptStatus.DRAFT })
    @IsOptional()
    @IsEnum(GoodsReceiptStatus)
    status?: GoodsReceiptStatus;

    // @ApiProperty({ description: 'รหัส Purchase Order', required: false })
    // @IsOptional()
    // @IsNumber()
    // purchaseOrderId?: number;

    @ApiProperty({ description: 'รหัส Purchase Order', required: false })
    @IsOptional()
    poNumber?: string;

    @ApiProperty({ description: 'รหัส Queue', required: false })
    @IsOptional()
    queueNo?: string;

    @ApiProperty({ description: 'รหัสผู้จำหน่าย' })
    @IsNumber()
    @IsNotEmpty()
    supplierId: number;

    @ApiProperty({ description: 'รหัสสาขา' })
    @IsNumber()
    @IsNotEmpty()
    branchId: number;

    @ApiProperty({ description: 'รหัสคลังสินค้า' })
    @IsNumber()
    @IsNotEmpty()
    warehouseId: number;

    @ApiProperty({ description: 'วิธีการชำระเงิน' })
    @IsNumber()
    @IsNotEmpty()
    paymentMethodId: number;

    @ApiProperty({ description: 'มีvat', default: false })
    @IsOptional()
    @IsBoolean()
    isVat?: boolean;

    @ApiProperty({ description: 'ยอดรวมก่อนภาษี', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    subtotal?: number;
    
    @ApiProperty({ description: 'อัตราภาษี (%)', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    taxRate?: number;

    @ApiProperty({ description: 'จำนวนเงินภาษี', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    taxAmount?: number;

    @ApiProperty({ description: 'ยอดรวม', default: 0 })
    @IsOptional()
    @IsNumber()
    totalAmount?: number;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({ description: 'เลขที่ใบส่งของ', required: false })
    @IsOptional()
    @IsString()
    deliveryNote?: string;

    @ApiProperty({ description: 'วันที่ส่งของ', required: false })
    @IsOptional()
    @IsDateString()
    deliveryDate?: string;

    // @ApiProperty({ description: 'รหัสผู้รับสินค้า' })
    // @IsNumber()
    // @IsNotEmpty()
    // receivedById: number;

    @ApiProperty({ description: 'รหัสผู้อนุมัติ', required: false })
    @IsOptional()
    @IsNumber()
    approvedById?: number;

    @ApiProperty({ description: 'วันที่อนุมัติ', required: false })
    @IsOptional()
    @IsDateString()
    approvedAt?: string;

    @ApiProperty({ description: 'รายการสินค้า', type: [CreateGoodsReceiptItemDto] })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreateGoodsReceiptItemDto)
    items: CreateGoodsReceiptItemDto[];
}
