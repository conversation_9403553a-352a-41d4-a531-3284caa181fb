import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString, Min } from 'class-validator';

export class CreateQuotationItemDto {
    @ApiProperty({ description: 'รหัสสินค้า' })
    @IsNumber()
    @IsNotEmpty()
    productId: number;

    @ApiProperty({ description: 'จำนวนที่เสนอ' })
    @IsNumber()
    @IsNotEmpty()
    @Min(0.01)
    quantity: number;

    @ApiProperty({ description: 'ราคาต่อหน่วย' })
    @IsNumber()
    @IsNotEmpty()
    @Min(0)
    unitPrice: number;

    @ApiProperty({ description: 'ราคารวม' })
    @IsNumber()
    @IsNotEmpty()
    @Min(0)
    totalPrice: number;

    @ApiProperty({ description: 'ต้นทุนต่อหน่วย', required: false })
    @IsOptional()
    @IsNumber()
    @Min(0)
    unitCost?: number;

    @ApiProperty({ description: 'ส่วนลดรายการ (%)', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    discount?: number;

    @ApiProperty({ description: 'จำนวนเงินส่วนลด', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    discountAmount?: number;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;
}
