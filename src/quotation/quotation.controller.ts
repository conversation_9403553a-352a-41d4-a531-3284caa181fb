import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Put, 
  Param, 
  Delete, 
  HttpCode, 
  HttpStatus, 
  ParseIntPipe, 
  Req,
  Patch
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { Request } from 'express';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { Auth } from '../auth/decorators/auth.decorator';
import { QuotationService, QUOTATION_PAGINATION_CONFIG } from './quotation.service';
import { CreateQuotationDto } from './dto/create-quotation.dto';
import { UpdateQuotationDto } from './dto/update-quotation.dto';
import { QuotationStatus } from './entities/quotation.entity';

@Controller('quotation')
@ApiTags('ใบเสนอราคา (Quotation)')
@Auth()
export class QuotationController {
  constructor(private readonly quotationService: QuotationService) {}

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'ดึงข้อมูลใบเสนอราคาแบบ pagination' })
  @ApiPaginationQuery(QUOTATION_PAGINATION_CONFIG)
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  datatables(@Paginate() query: PaginateQuery) {
    return this.quotationService.datatables(query);
  }

  @Get('generate-quotation-number')
  @ApiOperation({ summary: 'สร้างเลขที่ใบเสนอราคาอัตโนมัติ' })
  @ApiResponse({ status: 200, description: 'สร้างเลขที่สำเร็จ' })
  generateQuotationNumber() {
    return this.quotationService.generateQuotationNumber();
  }

  @Post()
  @ApiOperation({ summary: 'สร้างใบเสนอราคาใหม่' })
  @ApiResponse({ status: 201, description: 'สร้างใบเสนอราคาสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  create(@Req() req: Request, @Body() createQuotationDto: CreateQuotationDto) {
    const userId = req.user['sub'];
    return this.quotationService.create(createQuotationDto, userId);
  }

  @Get()
  @ApiOperation({ summary: 'ดึงข้อมูลใบเสนอราคาทั้งหมด' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  findAll() {
    return this.quotationService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'ดึงข้อมูลใบเสนอราคาตาม ID' })
  @ApiParam({ name: 'id', description: 'รหัสใบเสนอราคา' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบเสนอราคา' })
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.quotationService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'อัปเดตใบเสนอราคา' })
  @ApiParam({ name: 'id', description: 'รหัสใบเสนอราคา' })
  @ApiResponse({ status: 200, description: 'อัปเดตสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบเสนอราคา' })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  update(
    @Param('id', ParseIntPipe) id: number, 
    @Req() req: Request,
    @Body() updateQuotationDto: UpdateQuotationDto
  ) {
    const userId = req.user['sub'];
    return this.quotationService.update(id, updateQuotationDto, userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'ลบใบเสนอราคา' })
  @ApiParam({ name: 'id', description: 'รหัสใบเสนอราคา' })
  @ApiResponse({ status: 200, description: 'ลบสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบเสนอราคา' })
  @ApiResponse({ status: 400, description: 'ไม่สามารถลบได้' })
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.quotationService.remove(id);
  }

  @Patch(':id/approve')
  @ApiOperation({ summary: 'อนุมัติใบเสนอราคา' })
  @ApiParam({ name: 'id', description: 'รหัสใบเสนอราคา' })
  @ApiResponse({ status: 200, description: 'อนุมัติสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบเสนอราคา' })
  @ApiResponse({ status: 400, description: 'ไม่สามารถอนุมัติได้' })
  approve(@Param('id', ParseIntPipe) id: number, @Req() req: Request) {
    const userId = req.user['sub'];
    return this.quotationService.approve(id, userId);
  }

  @Patch(':id/reject')
  @ApiOperation({ summary: 'ปฏิเสธใบเสนอราคา' })
  @ApiParam({ name: 'id', description: 'รหัสใบเสนอราคา' })
  @ApiResponse({ status: 200, description: 'ปฏิเสธสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบเสนอราคา' })
  @ApiResponse({ status: 400, description: 'ไม่สามารถปฏิเสธได้' })
  reject(@Param('id', ParseIntPipe) id: number, @Req() req: Request) {
    const userId = req.user['sub'];
    return this.quotationService.reject(id, userId);
  }

  @Patch(':id/expire')
  @ApiOperation({ summary: 'ทำเครื่องหมายใบเสนอราคาเป็นหมดอายุ' })
  @ApiParam({ name: 'id', description: 'รหัสใบเสนอราคา' })
  @ApiResponse({ status: 200, description: 'อัปเดตสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบเสนอราคา' })
  @ApiResponse({ status: 400, description: 'ไม่สามารถทำเครื่องหมายได้' })
  markAsExpired(@Param('id', ParseIntPipe) id: number) {
    return this.quotationService.markAsExpired(id);
  }
}
