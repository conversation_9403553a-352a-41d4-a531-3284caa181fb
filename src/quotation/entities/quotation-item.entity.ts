import { Entity, Column, ManyToOne } from 'typeorm';
import { CustomBaseEntity } from '../../common/entities/custom-base.entity';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';
import { Quotation } from './quotation.entity';
import { Product } from '../../product/entities/product.entity';

@Entity()
export class QuotationItem extends CustomBaseEntity {
    @ManyToOne(() => Quotation, (quotation) => quotation.items, { onDelete: 'CASCADE' })
    quotation: Quotation;

    @ManyToOne(() => Product, (product) => product.quotationItems)
    product: Product;

    @Column()
    productName: string; // ชื่อสินค้า (เก็บไว้เผื่อสินค้าถูกลบ)

    @Column()
    productCode: string; // รหัสสินค้า (เก็บไว้เผื่อสินค้าถูกลบ)

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    quantity: number; // จำนวนที่เสนอ

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    unitPrice: number; // ราคาต่อหน่วย

    @Column({ nullable: true })
    unitName: string; // ชื่อหน่วย

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    totalPrice: number; // ราคารวม

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), nullable: true })
    unitCost: number; // ต้นทุนต่อหน่วย

    @Column({ type: 'text', nullable: true })
    notes: string; // หมายเหตุ

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    discount: number; // ส่วนลดรายการ

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    discountAmount: number; // จำนวนเงินส่วนลด
}
