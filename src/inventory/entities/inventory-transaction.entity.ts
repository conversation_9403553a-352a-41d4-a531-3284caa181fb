import { Column, Entity, Index, ManyToOne } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { Inventory } from "./inventory.entity";
import { User } from "../../user/entities/user.entity";
import { Supplier } from "../../supplier/entities/supplier.entity";

export enum TransactionType {
    IN = "in",                    // รับเข้า
    OUT = "out",                  // จ่ายออก
    TRANSFER_IN = "transfer_in",  // โอนเข้า
    TRANSFER_OUT = "transfer_out", // โอนออก
    ADJUSTMENT = "adjustment",    // ปรับปรุง
    PURCHASE = "purchase",        // ซื้อเข้า
    SALE = "sale",               // ขายออก
    RETURN = "return",           // คืนสินค้า
    DAMAGE = "damage",           // สินค้าเสียหาย
    EXPIRED = "expired"          // สินค้าหมดอายุ
}

@Entity()
export class InventoryTransaction extends CustomBaseEntity {
    @ManyToOne(() => Inventory, (inventory) => inventory.transactions, { onDelete: 'CASCADE' })
    inventory: Inventory;

    @Column({ type: 'enum', enum: TransactionType })
    type: TransactionType;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    quantity: number; // จำนวนที่เปลี่ยนแปลง (+/-)

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    balanceAfter: number; // ยอดคงเหลือหลังทำรายการ

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), nullable: true })
    unitCost: number; // ต้นทุนต่อหน่วย

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), nullable: true })
    totalCost: number; // ต้นทุนรวม

    @Column({ nullable: true })
    referenceType: string; // ประเภทเอกสารอ้างอิง เช่น 'purchase_order', 'sales_order'

    @Column({ nullable: true })
    referenceId: number; // รหัสเอกสารอ้างอิง

    @Column({ nullable: true })
    referenceNumber: string; // เลขที่เอกสารอ้างอิง

    @Column()
    transactionDate: Date; // วันที่ทำรายการ

    @Column({ type: 'text', nullable: true })
    description: string; // รายละเอียด

    @Column({ type: 'text', nullable: true })
    notes: string; // หมายเหตุ

    @ManyToOne(() => Supplier, { nullable: true })
    supplier: Supplier; // ผู้จำหน่าย (สำหรับรายการที่มาจากการซื้อ)

    constructor(partial?: Partial<InventoryTransaction>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }
}
