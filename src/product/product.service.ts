import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateProductDto, Type } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Product } from './entities/product.entity';
import { DataSource, FindOptionsWhere, In, Repository } from 'typeorm';
import {
    FilterOperator,
    PaginateConfig,
    PaginateQuery,
    Paginated,
    paginate,
} from 'nestjs-paginate';
import { CreateProductAttributeDto } from './dto/create-product-attribute.dto';
import { ProductAttribute } from './entities/product-attribute.entity';
import { ProductAttributeValue } from './entities/product-attribute-value.entity';
import { ConfigService } from '@nestjs/config';
import { imagePath } from 'src/common/helper';
import { ProductRepository } from './product.repository';
import { QueueItem } from 'src/queue/entities/queue-item.entity';
import { DateTime } from 'luxon';
import { GoodsReceiptItem, GoodsReceiptStatus } from 'src/goods-receipt/entities';
import { SaleItem, SaleStatus } from 'src/sale/entities';

export const PRODUCT_PAGINATION_CONFIG: PaginateConfig<Product> = {
    sortableColumns: [
        'id',
        'code',
        'name',
        'price',
        'category.name',
        'unit.name',
        'branch.name'
    ],
    relations: {
        branch: true,
        category: true,
        unit: true,
        image: true,
        goodsReceiptItems: {
            goodsReceipt: true,
        },
        saleItems: {
            sale: true,
        }
    },
    defaultSortBy: [['code', 'ASC']],
    searchableColumns: ['code', 'name', 'branch.name'],
    filterableColumns: {
        'category.id': [FilterOperator.EQ],
        // 'productLevels.level.id': [FilterOperator.EQ],
        'branch.id': [FilterOperator.EQ],
    },
};

@Injectable()
export class ProductService {
    constructor(
        private dataSource: DataSource,
        private productRepository: ProductRepository,
    ) { }

    async create(createProductDto: CreateProductDto) {
        const product = this.productRepository.repository.create({
            ...createProductDto,
            category: {
                id: createProductDto.categoryId,
            },
            unit: {
                id: createProductDto.unitId,
            },
        });

        await this.productRepository.repository.save(product);

        return this.findOne(product.id);
    }

    async findAll(filter: { categoryId: number; levelId: number }) {
        let where: FindOptionsWhere<Product> = {};

        if (filter?.categoryId) {
            where = {
                ...where,
                category: {
                    id: filter.categoryId,
                },
            };
        }

        if (filter?.levelId) {
            where = {
                ...where,
            };
        }

        const products = await this.productRepository.repository.find({
            where: where,
            relations: {
                category: true,
                unit: true,
                image: true,
            },
            order: {
                code: 'ASC',
            },
        });

        return products;
    }

    async findOne(id: number) {
        const product = await this.productRepository.repository.findOne({
            select: {
                category: {
                    id: true,
                    code: true,
                    name: true,
                },
                unit: {
                    id: true,
                    name: true,
                },
                branch: {
                    id: true,
                    name: true,
                },
            },
            where: { id },
            relations: {
                category: true,
                // productAttributes: {
                //   productAttributeValues: true,
                // },
                branch: true,
                unit: true,
                image: true,
            },
        });

        if (!product) throw new NotFoundException('product not found');

        return product;
    }

    async update(id: number, updateProductDto: UpdateProductDto) {
        const product = await this.findById(id);

        if (!product) throw new NotFoundException('product not found');

        await this.productRepository.updateProductLevel(id, updateProductDto);

        return this.findById(product.id);
    }

    async remove(id: number) {
        const product = await this.findById(id);

        if (!product) throw new NotFoundException('product not found');

        const now = +new Date();

        await this.productRepository.repository.update(id, {
            code: `${now}${product.code}`,
        });

        await this.productRepository.repository.softRemove(product);
    }

    findById(id: number) {
        return this.productRepository.repository.findOne({
            where: {
                id,
            },
            relations: {
                category: true,
                image: true,
            },
        });
    }

    async datatables(query: PaginateQuery): Promise<Paginated<Product>> {
        const products = await paginate(
            query,
            this.productRepository.repository,
            PRODUCT_PAGINATION_CONFIG,
        );

        // for (const product of products.data) {
        //     let totalStock = 0;

        //     totalStock += product.goodsReceiptItems
        //         .filter(item => item.goodsReceipt.status === GoodsReceiptStatus.COMPLETED)
        //         .reduce((sum, item) => sum + item.receivedQuantity, 0);

        //     totalStock -= product.saleItems
        //         .filter(item => item.sale.status === SaleStatus.COMPLETED)
        //         .reduce((sum, item) => sum + item.quantity, 0);

        //     product['totalStock'] = totalStock;

        //     // ลบความสัมพันธ์ที่ไม่ต้องการแสดงในผลลัพธ์
        //     delete product.goodsReceiptItems;
        //     delete product.saleItems;
        // }

        return products;
    }

    // findProductPriceByName(name: string, productId: number) {
    //   return this.productPriceRepository.findOne({
    //     relations: {
    //       product: true,
    //     },
    //     where: {
    //       name: name,
    //       product: {
    //         id: productId,
    //       }
    //     }
    //   });
    // }

    // createAttribute(productId: number, dto: CreateProductAttributeDto) {
    //   return this.productRepository.createAttribute(productId, dto);
    // }

    async deleteAttribute(productAttributeId: number) {
        const isExist =
            await this.productRepository.findAttribute(productAttributeId);
        if (!isExist) {
            throw new NotFoundException('product attribute not found');
        }

        await this.productRepository.deleteAttribute(productAttributeId);
    }

    async createProductLevel(createProductDto: CreateProductDto) {
        const product = await this.productRepository.createProductLevel({
            ...createProductDto,
        });

        return this.findOne(product.id);
    }

    async purchaseHistory(productId: number) {
        const queueItems = await QueueItem.find({
            select: {
                createdAt: true,
                netWeight: true,
                price: true,
                total: true,
                productName: true,
            },
            where: {
                product: { id: productId },
            },
            order: {
                createdAt: 'DESC',
            }
        });

        if (!queueItems || queueItems.length === 0) {
            throw new NotFoundException('No purchase history found for this product.');
        }

        const history = queueItems.map(item => ({
            purchaseDate: DateTime.fromJSDate(item.createdAt).toFormat('yyyy-MM-dd'),
            quantity: item.netWeight,
            pricePerUnit: item.price,
            totalAmount: item.total,
        }));

        const totalPurchaseAmount = history.reduce((sum, item) => sum + Number(item.totalAmount || 0), 0);

        return {
            productName: queueItems[0].productName,
            history,
            totalPurchaseAmount,
        };
    }

    async getMovements(productId: number) {
        const movements: {
            date: string;
            number: string;
            productName: string;
            type: string;
            quantity: number;
            warehouse: string;
        }[] = [];

        const goodsReceiptItems = await GoodsReceiptItem.find({
            select: {
                createdAt: true,
                receivedQuantity: true,
                unitPrice: true,
                totalPrice: true,
            },
            where: {
                product: { id: productId },
                goodsReceipt: {
                    status: GoodsReceiptStatus.COMPLETED,
                },
            },
            order: {
                createdAt: 'DESC',
            },
            relations: {
                goodsReceipt: {
                    warehouse: true,
                },
                product: true,
            },
        });

        goodsReceiptItems.forEach(item => {
            movements.push({
                date: DateTime.fromJSDate(item.createdAt).toFormat('yyyy-MM-dd HH:mm:ss'),
                number: item.goodsReceipt.grNumber,
                productName: item.product.name,
                type: 'Goods Receipt',
                quantity: item.receivedQuantity,
                warehouse: item.goodsReceipt.warehouse.name,
            });
        });

        const saleItems = await SaleItem.find({
            select: {
                createdAt: true,
                quantity: true,
                unitPrice: true,
                totalPrice: true,
            },
            where: {
                product: { id: productId },
                sale: {
                    status: SaleStatus.COMPLETED,
                },
            },
            order: {
                createdAt: 'DESC',
            },
            relations: {
                sale: {
                    warehouse: true,
                },
                product: true,
            },
        });

        saleItems.forEach(item => {
            movements.push({
                date: DateTime.fromJSDate(item.createdAt).toFormat('yyyy-MM-dd HH:mm:ss'),
                number: item.sale.saleNumber,
                productName: item.product.name,
                type: 'Sale',
                quantity: -item.quantity,
                warehouse: item.sale.warehouse.name,
            });
        });

        //Sort by date
        movements.sort((a, b) => {
            return new Date(a.date).getTime() - new Date(b.date).getTime();
        });

        return movements;
    }
}
