import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, ValidateIf } from "class-validator";

export enum Type {
    COLOR = 'color',
    IMAGE = 'image',
}

export class CreateProductDto {
    @ApiProperty()
    readonly code: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly name: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly price: number;

    // readonly imageId?: number;
    @ApiProperty({ enum: Type })
    @IsEnum(Type)
    readonly type?: Type;

    @ApiProperty()
    @ValidateIf(o => o.type === Type.IMAGE)
    @IsNotEmpty({ message: 'ต้องระบุ imageId ถ้า type เป็น image' })
    readonly imageId?: number;

    @ApiProperty()
    @ValidateIf(o => o.type === Type.COLOR)
    @IsNotEmpty({ message: 'ต้องระบุ colorCode ถ้า type เป็น color' })
    readonly colorCode?: string;

    readonly categoryId?: number;
    readonly unitId: number;
    readonly branchId: number;
}
