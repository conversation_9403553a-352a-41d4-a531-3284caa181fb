import { Injectable } from '@nestjs/common';
import { Workbook } from 'exceljs';
import { ProductRepository } from './product.repository';
import { ProductService } from './product.service';
import { Product } from './entities/product.entity';

@Injectable()
export class ProductReportService {
  constructor(
    private productRepository: ProductRepository,
    private readonly productService: ProductService,
  ) { }

  async exportAll() {
    const workbook = new Workbook

    const worksheet = workbook.addWorksheet('product')

    worksheet.columns = [
      { header: 'ลำดับ', key: 'no' },
      { header: 'รหัสสินค้า', key: 'code' },
      { header: 'สินค้า', key: 'name' },
      { header: 'หน่วย', key: 'unitNname' },
      { header: 'ราคาทั่วไป', key: 'price' },
    ]

    const content = await this.productService.findAll(null)

    let data = content.map((e, i, _) => ({
      no: i + 1,
      code: e.code,
      name: e.name,
      unitNname: e?.unit?.name ?? '',
      price: e.price,
    }));

    data.forEach((val, i, _) => {
      worksheet.addRow(val)
    })

    return workbook.xlsx.writeBuffer();
  } 
}
