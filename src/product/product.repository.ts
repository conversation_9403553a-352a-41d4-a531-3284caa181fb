import { BadRequestException, Inject, Injectable, Scope } from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { Request } from "express";
import { BaseRepository } from "src/common/repository/base-repository";
import { DataSource } from "typeorm";
import { Product } from "./entities/product.entity";
import { CreateProductAttributeDto } from "./dto/create-product-attribute.dto";
import { ProductAttribute } from "./entities/product-attribute.entity";
import { ProductAttributeValue } from "./entities/product-attribute-value.entity";
import { CreateProductDto } from "./dto/create-product.dto";
import { UpdateProductDto } from "./dto/update-product.dto";
import { Branch } from "src/branch/entities/branch.entity";
import { Category } from "src/category/entities/category.entity";

@Injectable({ scope: Scope.REQUEST })
export class ProductRepository extends BaseRepository {
    constructor(dataSource: DataSource, @Inject(REQUEST) req: Request) {
        super(dataSource, req);
    }

    get repository() {
        return this.getRepository(Product);
    }

    // async createAttribute(productId: number, attributeDto: CreateProductAttributeDto) {
    //   const proAttRepository = this.getRepository(ProductAttribute);
    //   const proAttValueRepository = this.getRepository(ProductAttributeValue);

    //   const { attributeValues, ...attribute } = attributeDto;

    //   const productAttribute = proAttRepository.create({ ...attribute, product: { id: productId } });
    //   await proAttRepository.save(productAttribute);

    //   const attributeValuesData = attributeValues.map((e) => proAttValueRepository.create({ ...e, productAttribute: { id: productAttribute.id, } }));

    //   await proAttValueRepository.save(attributeValuesData);

    //   return productAttribute
    // }

    async deleteAttribute(productAttributeId: number) {
        const proAttRepository = this.getRepository(ProductAttribute);

        await proAttRepository.softDelete(productAttributeId);
    }

    findAttribute(attributeId: number) {
        return this.getRepository(ProductAttribute).findOneBy({ id: attributeId })
    }

    async createProductLevel(createProductDto: CreateProductDto): Promise<Product> {
        const productRepository = this.getRepository(Product);

        const { unitId, categoryId, imageId, branchId, ...data } = createProductDto;

        let code = '';
        const branch = await Branch.findOneBy({ id: branchId })
        const category = await Category.findOneBy({ id: categoryId })

        if (!branch || !category) {
            throw new BadRequestException("Invalid branch or category ID.");
        }

        let lastRunNumber = 0;

        const lastProduct = await Product.find({
            where: {
                category: { id: categoryId },
                branch: { id: branchId }
            },
            take: 1,
            order: {
                code: 'DESC'
            }
        })

        if (lastProduct.length > 0) {
            lastRunNumber = +lastProduct[0].code.slice(4, 7);
        }

        if (createProductDto.code) {
            code = createProductDto.code;
        } else {
            code = this.genCode(branch.code, category.code, lastRunNumber);
        }

        //check dupicate code
        const isDuplicateCode = await Product.findOneBy({ code: code });
        if (isDuplicateCode) {
            throw new BadRequestException(code + " รหัสสินค้านี้มีอยู่แล้ว");
        }

        const product = productRepository.create({
            ...data,
            code: code,
            unit: {
                id: unitId,
            },
            category: {
                id: categoryId,
            },
            image: {
                id: imageId,
            },
            branch: {
                id: branchId
            }
        });
        await productRepository.save(product);

        return product;
    }

    async updateProductLevel(productId: number, updateProductDto: UpdateProductDto): Promise<void> {
        const productRepository = this.getRepository(Product);

        const { unitId, categoryId, imageId, branchId, type, ...updateData } = updateProductDto;

        await productRepository.update(productId, {
            ...updateData,
            unit: {
                id: unitId ?? null,
            },
            category: {
                id: categoryId ?? null,
            },
            image: {
                id: imageId ?? null,
            },
            branch: {
                id: branchId
            },
            type: type,
        });

    }

    private genCode(branchCode: string, categoryCode: string, lastRunNumber: number) {
        // Format branch code and category code to ensure they're 2 characters
        const formattedBranchCode = branchCode.padStart(2, '0').slice(0, 2);
        const formattedCategoryCode = categoryCode.padStart(2, '0').slice(0, 2);

        // Start the sequence at 001 if lastRunNumber is 0
        const newRunNumber = (lastRunNumber + 1).toString().padStart(3, '0');

        // Combine all parts to form the product code
        return `${formattedBranchCode}${formattedCategoryCode}${newRunNumber}`;
    }
}