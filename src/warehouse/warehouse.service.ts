import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Warehouse, WarehouseStatus } from './entities/warehouse.entity';
import { CreateWarehouseDto } from './dto/create-warehouse.dto';
import { UpdateWarehouseDto } from './dto/update-warehouse.dto';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';
import { CATEGORY_PAGINATION_CONFIG } from 'src/category/category.service';

export const WAREHOUSE_PAGINATION_CONFIG: PaginateConfig<Warehouse> = {
  sortableColumns: ['id', 'code', 'name'],
  searchableColumns: ['code', 'name'],
  filterableColumns: {
    status: true,
    'branch.id': true,
  },
  relativePath: true,
  relations: {
    branch: true,
  }
};

@Injectable()
export class WarehouseService {
  constructor(
    @InjectRepository(Warehouse)
    private warehouseRepository: Repository<Warehouse>,
  ) { }

  async create(createWarehouseDto: CreateWarehouseDto): Promise<Warehouse> {
    // ตรวจสอบรหัสซ้ำ
    const existingWarehouse = await this.warehouseRepository.findOne({
      where: { code: createWarehouseDto.code }
    });

    if (existingWarehouse) {
      throw new BadRequestException(`Warehouse code ${createWarehouseDto.code} already exists`);
    }

    const warehouse = this.warehouseRepository.create({
      ...createWarehouseDto,
      branch: {
        id: createWarehouseDto.branchId
      } as any
    });

    return await this.warehouseRepository.save(warehouse);
  }

  async findAll(): Promise<Warehouse[]> {
    return this.warehouseRepository.find({
      relations: {
        branch: true
      },
      order: {
        createdAt: 'DESC'
      }
    });
  }

  async findByBranch(branchId: number): Promise<Warehouse[]> {
    return this.warehouseRepository.find({
      where: {
        branch: { id: branchId }
      },
      relations: {
        branch: true
      },
      order: {
        name: 'ASC'
      }
    });
  }

  async findOne(id: number): Promise<Warehouse> {
    const warehouse = await this.warehouseRepository.findOne({
      where: { id },
      relations: {
        branch: true,
        inventories: {
          product: true
        }
      }
    });

    if (!warehouse) {
      throw new NotFoundException(`Warehouse with ID ${id} not found`);
    }

    return warehouse;
  }

  async findByCode(code: string): Promise<Warehouse> {
    const warehouse = await this.warehouseRepository.findOne({
      where: { code },
      relations: {
        branch: true
      }
    });

    if (!warehouse) {
      throw new NotFoundException(`Warehouse with code ${code} not found`);
    }

    return warehouse;
  }

  async update(id: number, updateWarehouseDto: UpdateWarehouseDto): Promise<Warehouse> {
    const warehouse = await this.findOne(id);

    // ตรวจสอบรหัสซ้ำ (ถ้ามีการเปลี่ยนรหัส)
    if (updateWarehouseDto.code && updateWarehouseDto.code !== warehouse.code) {
      const existingWarehouse = await this.warehouseRepository.findOne({
        where: { code: updateWarehouseDto.code }
      });

      if (existingWarehouse) {
        throw new BadRequestException(`Warehouse code ${updateWarehouseDto.code} already exists`);
      }
    }

    const updateData: any = { ...updateWarehouseDto };
    if (updateWarehouseDto.branchId) {
      updateData.branch = { id: updateWarehouseDto.branchId };
      delete updateData.branchId;
    }

    await this.warehouseRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const warehouse = await this.findOne(id);

    // ตรวจสอบว่ามี inventory อยู่หรือไม่
    if (warehouse.inventories && warehouse.inventories.length > 0) {
      throw new BadRequestException('Cannot delete warehouse that has inventory items');
    }

    await this.warehouseRepository.softDelete(id);
  }

  async changeStatus(id: number, status: WarehouseStatus): Promise<Warehouse> {
    await this.warehouseRepository.update(id, { status });
    return this.findOne(id);
  }

  async generateWarehouseCode(branchId: number): Promise<string> {
    // หา warehouse ล่าสุดในสาขานี้
    const lastWarehouse = await this.warehouseRepository
      .createQueryBuilder('warehouse')
      .where('warehouse.branchId = :branchId', { branchId })
      .orderBy('warehouse.code', 'DESC')
      .getOne();

    const branchCode = branchId.toString().padStart(2, '0');
    let sequence = 1;

    if (lastWarehouse) {
      // สมมติรูปแบบ: WH{BB}{NN} เช่น WH0101
      const lastSequence = parseInt(lastWarehouse.code.slice(-2));
      sequence = lastSequence + 1;
    }

    return `WH${branchCode}${sequence.toString().padStart(2, '0')}`;
  }

  async datatables(query: PaginateQuery): Promise<Paginated<Warehouse>> {
    return paginate(query, this.warehouseRepository, WAREHOUSE_PAGINATION_CONFIG);
  }
}
