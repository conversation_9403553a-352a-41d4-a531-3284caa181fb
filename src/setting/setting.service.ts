import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { CreateSettingDto } from './dto/create-setting.dto';
import { UpdateSettingDto } from './dto/update-setting.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Setting } from './entities/setting.entity';

@Injectable()
export class SettingService implements OnApplicationBootstrap {

  private logger = new Logger(SettingService.name);

  constructor(
    @InjectRepository(Setting)
    private settingRepository: Repository<Setting>,
  ) { }

  onApplicationBootstrap() {
    this.initialSetting();
  }

  async initialSetting() {
    this.logger.verbose('initial setting');
    
    const data = [
      { name: 'TIME_FOR_PAY', value: null },
    ];

    for (const item of data) {
      const setting = await this.settingRepository.findOneBy({ name: item.name });
      if (!setting) {
        await this.settingRepository.save(item);
        this.logger.verbose(`create setting ${item.name}`);
      }
    }
  }

}
