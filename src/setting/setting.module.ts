import { Module } from '@nestjs/common';
import { SettingService } from './setting.service';
import { SettingController } from './setting.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Setting } from './entities/setting.entity';

@Module({
  // controllers: [SettingController],
  imports: [
    TypeOrmModule.forFeature([Setting])
  ],
  providers: [SettingService],
  exports: [SettingService],
})
export class SettingModule {}
