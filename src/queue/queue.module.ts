import { Module } from '@nestjs/common';
import { QueueService } from './queue.service';
import { QueueController } from './queue.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Queue } from './entities/queue.entity';
import { Branch } from '../branch/entities/branch.entity';
import { NotificationModule } from 'src/notification/notification.module';

@Module({
    imports: [
        TypeOrmModule.forFeature([Queue, Branch]),
        NotificationModule,
    ],
    controllers: [QueueController],
    providers: [QueueService],
})
export class QueueModule { }
