import { Column, Entity, ManyToMany, ManyToOne, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { User } from "../../user/entities/user.entity";
import { QueueItem } from "./queue-item.entity";
import { Branch } from "../../branch/entities/branch.entity";
import { QueuePhoto } from "./queue-photo.entity";
import { CarType } from "./car-type.entity";
import { Upload } from "../../upload/entities/upload.entity";
import { Supplier } from "../../supplier/entities";
import { GoodsReceipt } from "../../goods-receipt/entities";

export enum QueueStatus {
    WAITING = 'waiting',
    WEIGHED = 'weighed',
    SORTED = 'sorted',
    DONE = 'done',
    CANCELLED = 'cancelled',
}

export enum AliveStatus {
    YES = 'yes',
    NO = 'no',
}

export enum PurposeStatus {
    SEND = 'send',
    RECEIVE = 'receive',
    OTHER = 'other'
}

// export enum CarTypeEnum {
//     MORTOCYCLE = 'motorcycle',
//     CAR = 'car',
//     TRUCK = 'truck',
//     SIX_TRUCK = 'six_truck',
//     OTHER = 'other'
// }

@Entity()
export class Queue extends CustomBaseEntity {
    @Column()
    queueNo: string;

    @Column({ type: 'date' })
    queueDate: Date;

    @Column()
    queueNumber: number;

    @Column()
    idCard: string;

    @Column()
    customerName: string;

    @Column({ nullable: true })
    customerTax: string;

    @Column({ nullable: true })
    customerCompany: string;

    @Column({ nullable: true })
    phoneNumber: string;

    @Column({ nullable: true })
    customerAddress: string;

    @Column({ nullable: true })
    vehicleNumber: string;

    @Column({ nullable: true })
    department: string;

    @Column({ default: 0 })
    followerCount: number;

    @ManyToOne(() => Upload, (_) => _.coverPhotos)
    coverPhoto: Upload;

    @ManyToMany(() => Upload, (_) => _.identityCards)
    identityCards: Upload[];

    @Column('time', { nullable: true })
    arrivalTime: string;

    @Column('time', { nullable: true })
    doneTime: string;

    @Column({ nullable: true })
    otherCarType: string;

    @Column('numeric', { nullable: true, transformer: new DecimalColumnTransformer() })
    entranceWeight: number;

    @Column('time', { nullable: true })
    entranceTime: string;

    @Column('numeric', { nullable: true, transformer: new DecimalColumnTransformer() })
    exitWeight: number;

    @Column('time', { nullable: true })
    exitTime: string;

    @Column({ type: 'enum', enum: QueueStatus, default: QueueStatus.WAITING })
    status: QueueStatus;

    @Column({ nullable: true })
    skipNote: string;

    @Column('numeric', { nullable: true, transformer: new DecimalColumnTransformer() })
    total: number;

    @Column('boolean', { default: true })
    alive: boolean;

    @Column({ type: 'enum', enum: PurposeStatus, nullable: true })
    purpose: PurposeStatus;

    @Column({ nullable: true })
    otherPurposeDetail: string;

    @Column({ nullable: true })
    productType: string;

    // @Column({ type: 'enum', enum: AliveStatus, default: AliveStatus.YES })
    // alive: AliveStatus;
    // @Column('simple-array', { nullable: true })
    // photos: string[]; // ชื่อไฟล์รูป เช่น ['q1_1.jpg', 'q1_2.jpg']

    @OneToMany(() => QueuePhoto, (_) => _.queue, { cascade: true })
    photos: QueuePhoto[];

    @ManyToOne(() => User, (_) => _.queues)
    createdBy: User;

    @ManyToOne(() => User, (_) => _.servedQueues)
    servedBy: User;

    @ManyToOne(() => User, (_) => _.officerQueues)
    officer: User;

    @OneToMany(() => QueueItem, (_) => _.queue, { cascade: true })
    queueItems: QueueItem[];

    @ManyToOne(() => Branch, (_) => _.queues)
    branch: Branch;

    @ManyToOne(() => Supplier, (_) => _.queues, { nullable: true })
    supplier: Supplier;

    @ManyToOne(() => CarType, (_) => _.queues, { nullable: true })
    carType: CarType;

    @Column({ nullable: true })
    readyToPaid: Date;

    @Column({ nullable: true })
    estimatedPaidAt: Date;

    @Column({ nullable: true })
    paidAt: Date;

    @Column({ nullable: true })
    incomplete: boolean;

    @OneToMany(() => GoodsReceipt, (gr) => gr.queue, { nullable: true })
    goodsReceipts: GoodsReceipt[];
}