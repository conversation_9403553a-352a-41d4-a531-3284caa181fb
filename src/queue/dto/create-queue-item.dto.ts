import { ApiProperty } from "@nestjs/swagger";
import { IsNumber, IsString, IsArray, ValidateNested, IsOptional, IsBoolean, IsEnum, IsNotEmpty } from "class-validator";
import { Type } from "class-transformer";
import { QueueStatus } from "../entities/queue.entity";

export class QueuePhotoDto {
    @IsNotEmpty()
    @ApiProperty({ example: 1, description: 'ID ของรูปที่ upload' })
    readonly uploadId: number;

    @IsNotEmpty()
    @IsNumber()
    @ApiProperty({ example: 10, description: 'น้ำหนักสินค้าในรูป' })
    readonly weight: number;

    @ApiProperty({ example: true, required: false })
    @IsOptional()
    @IsBoolean()
    readonly checkItem?: boolean;
}

export class QueueItemAllowanceDto {
    @ApiProperty({ example: 'น้ำ' })
    @IsOptional()
    @IsString()
    readonly name?: string;

    @ApiProperty({ example: 0.5 })
    @IsOptional()
    @IsNumber()
    readonly weight?: number;
}


export class CreateQueueItemDto {
    @ApiProperty({ example: 'เขียนโน๊ต' })
    @IsString()
    readonly note?: string;

    @ApiProperty({ example: 175, description: 'รหัส ID สินค้า' })
    @IsOptional()
    @IsNumber()
    readonly productId?: number;

    @ApiProperty({ example: 'กระป๋องอลูมิเนียม' })
    @IsString()
    readonly productName?: string;

    @ApiProperty({ example: 1, description: 'รหัส ID ของหน่วยสินค้า' })
    @IsOptional()
    @IsNumber()
    readonly unitId?: number;

    @ApiProperty({ example: 'กิโลกรัม' })
    @IsString()
    readonly unitName?: string;

    @ApiProperty({ example: true, required: false })
    @IsOptional()
    @IsBoolean()
    readonly accepted?: boolean;

    @ApiProperty({ example: 30, description: 'น้ำหนักสินค้าพร้อมภาชนะ' })
    @IsOptional()
    @IsNumber()
    readonly grossWeight?: number;

    @ApiProperty({ type: [QueuePhotoDto], required: true, description: 'รูป + น้ำหนักพร้อมภาชนะ' })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => QueuePhotoDto)
    readonly grossItem?: QueuePhotoDto[];

    @ApiProperty({ example: 5, description: 'น้ำหนักภาชนะ' })
    @IsOptional()
    @IsNumber()
    readonly tareWeight?: number;

    @ApiProperty({ type: [QueuePhotoDto], required: false, description: 'รูป + น้ำหนักภาชนะ' })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => QueuePhotoDto)
    readonly tareItem?: QueuePhotoDto[];

    @ApiProperty({ example: 0, description: 'น้ำหนักทั้หัก' })
    @IsOptional()
    @IsNumber()
    readonly deductedWeight?: number;

    @ApiProperty({ type: [QueuePhotoDto], required: false, description: 'รูป + น้ำหนักที่หัก'  })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => QueuePhotoDto)
    readonly allowance?: QueuePhotoDto[];

    @ApiProperty({ example: 24.5, description: 'น้ำหนักสินค้าหลังหักแล้ว' })
    @IsOptional()
    @IsNumber()
    readonly netWeight?: number;

    @ApiProperty({ example: 10, description: 'ราคาต่อหน่วย' })
    @IsOptional()
    @IsNumber()
    readonly price?: number;

    @ApiProperty({ example: 245, description: 'ราคารวม' })
    @IsOptional()
    @IsNumber()
    readonly total?: number;

    @ApiProperty({ type: [QueueItemAllowanceDto] })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => QueueItemAllowanceDto)
    readonly allowanceDropdown?: QueueItemAllowanceDto[];
}

export class CreateQueueItemArrayDto {
    @ApiProperty({ type: [CreateQueueItemDto] })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreateQueueItemDto)
    readonly queueItems?: CreateQueueItemDto[];

    @ApiProperty({ example: false, required: false, description: 'True = ยังชั่งน้ำหนักไม่เสร็จ' })
    @IsOptional()
    @IsBoolean()
    readonly incomplete?: boolean;

    @ApiProperty({
        example: QueueStatus.WEIGHED,
        enum: QueueStatus
    })
    @IsOptional()
    @IsEnum(QueueStatus)
    readonly status?: QueueStatus;
}