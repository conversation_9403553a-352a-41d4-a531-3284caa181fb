import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, Length, IsOptional, IsArray, IsEnum, IsNumber } from "class-validator";

import { QueuePhotoStatus } from "../entities/queue-photo.entity";
import { CreateQueuePhotoDto } from "./create-queue-photo.dto";
import { PurposeStatus } from "../entities/queue.entity";
import { CarType } from "../entities/car-type.entity";

export class CreateQueueDto {
    @ApiProperty({ example: '1234567890098' })
    @IsNotEmpty()
    @Length(13)
    readonly idCard: string;

    @ApiProperty({ example: 'แดง' })
    @IsNotEmpty()
    readonly customerName: string;

    @ApiProperty({ example: '123 ถนน' })
    @IsNotEmpty()
    readonly customerAddress: string;

    @ApiProperty({ example: '0812345678' })
    @IsOptional()
    readonly phoneNumber?: string;

    @ApiProperty({ example: 'กข 1234' })
    @IsOptional()
    readonly vehicleNumber?: string;

    // @ApiProperty({ example: ['q1_1.jpg', 'q1_2.jpg'] })
    // readonly photos?: string[];

    @ApiProperty({ type: [CreateQueuePhotoDto], example: [{ uploadId: 1, status: 'income' }] })
    @IsOptional()
    @IsArray()
    readonly photos?: CreateQueuePhotoDto[];

    @ApiProperty({ example: 'AshaTech' })
    @IsOptional()
    readonly customerCompany?: string;

    @ApiProperty({ example: 'ฝ่ายซื้อ' })
    @IsOptional()
    readonly department?: string

    @ApiProperty({ example: 2 })
    @IsOptional()
    readonly followerCount?: number;

    @ApiProperty({ example: 1})
    @IsNumber()
    readonly coverPhotoId?: number;

    @ApiProperty({ example: [1, 2], description: 'IDs ของ Upload' })
    readonly identityCardIds?: number[];

    @ApiProperty({ example: 6 })
    @IsOptional()
    readonly carTypeId?: number;

    @ApiProperty({ example: 'รถยนต์' })
    @IsOptional()
    readonly otherCarType?: string;

    @ApiProperty({ example: '1' })
    @IsOptional()
    readonly branchId?: number;

    @ApiProperty({ example: '1' })
    readonly visitorNo: number;

    @ApiProperty({
        example: PurposeStatus.SEND,
        enum: PurposeStatus
    })
    @IsEnum(PurposeStatus)
    @IsOptional()
    readonly purpose?: PurposeStatus;

    @ApiProperty({ example: 'เบิกออก' })
    @IsOptional()
    readonly otherPurposeDetail?: string;

    @ApiProperty({ example: 'ประเภทสินค้า' })
    @IsOptional()
    readonly productType?: string;

    @ApiProperty({ example: 250 })
    @IsOptional()
    readonly entranceWeight?: number;

    @ApiProperty({ example: '12:17:31' })
    @IsOptional()
    readonly entranceTime?: string;
    // readonly queueItems: CreateQueueItemDto[];
}

// export class CreateQueueItemDto {
//     readonly weight: number;
//     readonly note: string;
//     readonly product: number;
//     readonly unit: number;

// }