import { OrderPayment } from "../../order/entities/order-payment.entity";
import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, Index, OneToMany, Unique } from "typeorm";
import { Order } from "../../order/entities/order.entity";
import { Invoice } from "src/invoice/entities/invoice.entity";
import { GoodsReceipt } from "src/goods-receipt/entities";
import { Sale } from "src/sale/entities/sale.entity";
import { Quotation } from "src/quotation/entities/quotation.entity";

export enum PaymentMethodType {
    CASH = 'cash',
    // ONLINE = 'online',
    THAIQR = 'thaiqr',
}

@Entity()
@Unique(['name'])
export class PaymentMethod extends CustomBaseEntity {
    @Column()
    @Index({ unique: true })
    name: string;

    @Column({ nullable: true})
    icon: string;

    @Column({ type: 'enum', enum: PaymentMethodType})
    type: PaymentMethodType;

    @OneToMany(() => OrderPayment, (op: OrderPayment) => op.paymentMethod)
    orderPayments: OrderPayment[];

    @OneToMany(() => Order, (_) => _.paymentMethod)
    orders: Order[];

    @OneToMany(() => Invoice, (_) => _.paymentMethod)
    invoices: Invoice[];

    @OneToMany(() => GoodsReceipt, (gr) => gr.paymentMethod)
    goodsReceipts: GoodsReceipt[];

    @OneToMany(() => Sale, (sale) => sale.paymentMethod)
    sales: Sale[];

    // @OneToMany(() => Quotation, (quotation) => quotation.paymentMethod)
    // quotations: Quotation[];

    constructor(partial?: Partial<PaymentMethod>) {
        super();
        if (partial) {
            Object.assign(this, partial)
        }
    }
}

