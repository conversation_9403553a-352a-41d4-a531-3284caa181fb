import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsDateString } from 'class-validator';

export class CreateSaleItemDto {
    @ApiProperty({ description: 'รหัสสินค้า' })
    @IsNumber()
    @IsNotEmpty()
    productId: number;

    @ApiProperty({ description: 'จำนวนที่ขาย' })
    @IsNumber()
    @IsNotEmpty()
    quantity: number;

    @ApiProperty({ description: 'ราคาต่อหน่วย' })
    @IsNumber()
    @IsNotEmpty()
    unitPrice: number;

    @ApiProperty({ description: 'ราคารวม' })
    @IsNumber()
    @IsNotEmpty()
    totalPrice: number;

    @ApiProperty({ description: 'ต้นทุนต่อหน่วย', required: false })
    @IsOptional()
    @IsNumber()
    unitCost?: number;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;

    // @ApiProperty({ description: 'หมายเลข lot/batch', required: false })
    // @IsOptional()
    // @IsString()
    // batchNumber?: string;

    // @ApiProperty({ description: 'วันหมดอายุ', required: false })
    // @IsOptional()
    // @IsDateString()
    // expiryDate?: string;
}
