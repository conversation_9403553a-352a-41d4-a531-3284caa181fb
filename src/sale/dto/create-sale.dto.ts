import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsDateString, IsArray, ValidateNested, IsEnum, IsBoolean, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateSaleItemDto } from './create-sale-item.dto';
import { SaleStatus } from '../entities/sale.entity';

export class CreateSaleDto {
    @ApiProperty({ description: 'เลขที่ใบขาย' })
    @IsString()
    @IsNotEmpty()
    saleNumber: string;

    @ApiProperty({ description: 'วันที่ขาย' })
    @IsDateString()
    saleDate: string;

    @ApiProperty({ description: 'สถานะ', enum: SaleStatus, default: SaleStatus.DRAFT })
    @IsOptional()
    @IsEnum(SaleStatus)
    status?: SaleStatus;

    @ApiProperty({ description: 'รหัสลูกค้า' })
    @IsNumber()
    @IsNotEmpty()
    customerId: number;

    @ApiProperty({ description: 'รหัสสาขา' })
    @IsNumber()
    @IsNotEmpty()
    branchId: number;

    @ApiProperty({ description: 'รหัสคลังสินค้า' })
    @IsNumber()
    @IsNotEmpty()
    warehouseId: number;

    @ApiProperty({ description: 'วิธีการชำระเงิน' })
    @IsNumber()
    @IsNotEmpty()
    paymentMethodId: number;

    @ApiProperty({ description: 'ยอดรวม', default: 0 })
    @IsOptional()
    @IsNumber()
    subtotal?: number;

    @ApiProperty({ description: 'มีvat', default: false })
    @IsOptional()
    @IsBoolean()
    isVat?: boolean;

    @ApiProperty({ description: 'อัตราภาษี (%)', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    taxRate?: number;

    @ApiProperty({ description: 'จำนวนเงินภาษี', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    taxAmount?: number;

    @ApiProperty({ description: 'ส่วนลด', default: 0 })
    @IsOptional()
    @IsNumber()
    discount?: number;

    @ApiProperty({ description: 'ยอดรวมสุทธิ', default: 0 })
    @IsOptional()
    @IsNumber()
    totalAmount?: number;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({ description: 'รหัสผู้อนุมัติ', required: false })
    @IsOptional()
    @IsNumber()
    approvedById?: number;

    @ApiProperty({ description: 'วันที่อนุมัติ', required: false })
    @IsOptional()
    @IsDateString()
    approvedAt?: string;

    @ApiProperty({ description: 'รายการสินค้า', type: [CreateSaleItemDto] })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreateSaleItemDto)
    items: CreateSaleItemDto[];
}
