import { Column, Entity, Index, ManyToOne, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { User } from "../../user/entities/user.entity";
import { Branch } from "../../branch/entities/branch.entity";
import { Warehouse } from "../../warehouse/entities/warehouse.entity";
import { Customer } from "../../customer/entities/customer.entity";

import { PaymentMethod } from "src/payment-method/entities/payment-method.entity";
import { DeliveryNote } from "../../delivery-note/entities/delivery-note.entity";

export enum SaleStatus {
    DRAFT = "draft",           // ร่าง
    PENDING = "pending",       // รออนุมัติ
    APPROVED = "approved",     // อนุมัติแล้ว
    COMPLETED = "completed",   // เสร็จสิ้น
    CANCELLED = "cancelled",   // ยกเลิก
}

@Entity()
export class Sale extends CustomBaseEntity {
    @Index()
    @Column({ unique: true })
    saleNumber: string; // เลขที่ใบขาย

    @Column()
    saleDate: Date; // วันที่ขาย

    @Column({ type: 'enum', enum: SaleStatus, default: SaleStatus.DRAFT })
    status: SaleStatus;

    @ManyToOne(() => Customer, (customer) => customer.sales)
    customer: Customer;

    @ManyToOne(() => Branch, (branch) => branch.sales)
    branch: Branch;

    @ManyToOne(() => Warehouse, (warehouse) => warehouse.sales)
    warehouse: Warehouse;

    // @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    // subtotal: number;

    @Column({ type: 'boolean', default: false })
    isVat: boolean;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    taxRate: number;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    taxAmount: number;
    
    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    subtotal: number; // ยอดรวม

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    discount: number; // ส่วนลด

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    totalAmount: number; // ยอดรวมสุทธิ

    @Column({ type: 'text', nullable: true })
    notes: string; // หมายเหตุ

    @ManyToOne(() => User, (user) => user.sales)
    soldBy: User; // ผู้ขาย

    @ManyToOne(() => User, { nullable: true })
    approvedBy: User; // ผู้อนุมัติ

    @Column({ nullable: true })
    approvedAt: Date; // วันที่อนุมัติ

    @OneToMany('SaleItem', (item: any) => item.sale)
    items: any[];

    @ManyToOne(() => PaymentMethod, (pm) => pm.sales)
    paymentMethod: PaymentMethod;

    @OneToMany(() => DeliveryNote, (deliveryNote) => deliveryNote.sale)
    deliveryNotes: DeliveryNote[];

    // Reference to source quotation (if sale was created from quotation)
    @Column({ nullable: true })
    sourceQuotationId: number; // ID ของใบเสนอราคาที่สร้างใบขายนี้มา

    constructor(partial?: Partial<Sale>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }
}
