import { Column, Entity } from 'typeorm';
import { CustomBaseEntity } from '../../common/entities';
import { DecimalColumnTransformer } from 'src/common/decimal-column-transformer';

@Entity()
export class Allowance extends CustomBaseEntity {
    @Column()
    name: string;

    @Column({ type: 'numeric', nullable: true, transformer: new DecimalColumnTransformer(), default: 0 })
    weight: number;
}
