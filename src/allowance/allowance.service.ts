import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Allowance } from './entities/allowance.entity';
import { CreateAllowanceDto } from './dto/create-allowance.dto';
import { UpdateAllowanceDto } from './dto/update-allowance.dto';
import { paginate, PaginateConfig, Paginated, PaginateQuery } from 'nestjs-paginate';

export const ALLOWANCE_PAGINATION_CONFIG: PaginateConfig<Allowance> = {
    sortableColumns: ['id', 'name', 'weight'],
    searchableColumns: ['name', 'weight'],
    relativePath: true,
};

@Injectable()
export class AllowanceService {
    constructor(
        @InjectRepository(Allowance)
        private readonly allowanceRepo: Repository<Allowance>
    ) { }

    create(createAllowance: CreateAllowanceDto) {
        const allowance = this.allowanceRepo.create(createAllowance);
        return this.allowanceRepo.save(allowance);
    }

    findAll() {
        return this.allowanceRepo.find();
    }

    async findOne(id: number) {
        const item = await this.allowanceRepo.findOne({ where: { id } });
        if (!item) throw new NotFoundException('Allowance not found');
        return item;
    }

    async update(id: number, updateAllowance: UpdateAllowanceDto) {
        const allowance = await this.findOne(id);
        
        allowance.name = updateAllowance.name;
        allowance.weight = +updateAllowance.weight;

        return this.allowanceRepo.save(allowance);
    }

    async remove(id: number) {
        const allowance = await this.findOne(id);
        return this.allowanceRepo.remove(allowance);
    }

    async datatables(allowance: PaginateQuery): Promise<Paginated<Allowance>> {
        return paginate(allowance, Allowance.getRepository(), ALLOWANCE_PAGINATION_CONFIG);
    }

}