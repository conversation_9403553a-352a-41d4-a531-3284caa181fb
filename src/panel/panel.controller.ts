import { Controller, Get, Post, Body, Patch, Param, Delete, Put, HttpCode, HttpStatus, ClassSerializerInterceptor, UseInterceptors, Query } from '@nestjs/common';
import { PANEL_PAGINATION_CONFIG, PanelService } from './panel.service';
import { CreatePanelDto } from './dto/create-panel.dto';
import { UpdatePanelDto } from './dto/update-panel.dto';
import { ApiQuery, ApiTags } from '@nestjs/swagger';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';

@Controller('panel')
@ApiTags('หน้าจอขายสินค้า')
@UseInterceptors(ClassSerializerInterceptor)
export class PanelController {
  constructor(private readonly panelService: PanelService) {}

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(PANEL_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.panelService.datatables(query);
  }

  @Post()
  create(@Body() createPanelDto: CreatePanelDto) {
    return this.panelService.create(createPanelDto);
  }

  @Get()
  findAll() {
    return this.panelService.findAll();
  }

  @Get(':id')
  @ApiQuery({ name: 'levelId', required: false })
  findOne(@Param('id') id: string, @Query('levelId') levelId: string) {
    return this.panelService.findOne(+id);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updatePanelDto: UpdatePanelDto) {
    return this.panelService.update(+id, updatePanelDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.panelService.remove(+id);
  }
}
