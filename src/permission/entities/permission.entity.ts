import { CustomBaseEntity } from '../../common/entities/custom-base.entity';
import { Role } from '../../role/entities/role.entity';
import {
  Column,
  Entity,
  Index,
  ManyToMany,
  ManyToOne,
  Unique,
} from 'typeorm';
import { PermissionGroup } from './permission-group.entity';

export enum PermissionType {
  // User
  USER_READ = 'USER_READ',
  USER_CREATE = 'USER_CREATE',
  USER_UPDATE = 'USER_UPDATE',
  USER_DELETE = 'USER_DELETE',
}


@Entity()
@Unique(['name'])
export class Permission extends CustomBaseEntity {
  @Column({ comment: 'Permission name' })
  @Index({ unique: true })
  name: string;

  @Column({ nullable: true, comment: 'Permission description' })
  description: string;

  @ManyToMany(() => Role, (_) => _.permissions)
  roles: Array<Role>;

  @ManyToOne(() => PermissionGroup, (_) => _.permissions)
  permissionGroup: PermissionGroup;
}
