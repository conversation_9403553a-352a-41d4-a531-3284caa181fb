import { Controller, Get, StreamableFile, Query, Header, Param, <PERSON>s, Post, Body } from '@nestjs/common';
import { ReportService } from './report.service';
import { Readable } from 'stream';
import { ApiBody, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { DateTime } from 'luxon';

@Controller('report')
@ApiTags('รายงาน')
export class ReportController {
  constructor(private readonly reportService: ReportService) { }

  @Get('/order/excel')
  @Header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
  @Header('Content-Disposition', 'attachment; filename="report-order.xlsx"')
  async reportOrderExcel(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    // @Res({ passthrough: true }) res: Response
  ) {
    const dStartDate = new Date(startDate);
    const dEndDate = new Date(endDate);


    const content: any = await this.reportService.reportOrderExcel(dStartDate, dEndDate);

    const file = Readable.from(content);
    // res.set({
    //   'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    //   'Content-Disposition': `attachment; filename="report-order.xlsx"`,
    // });

    return new StreamableFile(file);
  }

  @Header('Cache-Control', 'none')
  @Header('Content-Type', 'application/pdf')
  @Post('/order/:id/pdf')
  // @Header('Content-Type', 'application/pdf')
  // @Header('Content-Disposition', 'attachment; filename=order.pdf')
  async reportOrderPdf(
    @Param('id') orderId: string,
    @Res() res: Response
  ) {
    const content = await this.reportService.reportOrderPdf1(+orderId);

    // const file = Readable.from(content);
    res.set({
      'Content-Disposition': `attachment; filename=order.pdf`,
      // 'Content-Disposition': `inline; filename=order.pdf`,
    });

    res.send(content);
    // return new StreamableFile(content);
  }

  @Post('/order/:id/pre-pdf')
  // @Header('Content-Type', 'application/pdf')
  // @Header('Content-Disposition', 'attachment; filename=order.pdf')
  async reportOrderPrePdf(
    @Param('id') orderId: string,
    @Res() res: Response
  ) {
    const content = await this.reportService.reportOrderPrePdf1(+orderId);

    // const file = Readable.from(content);
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename=order.pdf`,
      // 'Content-Disposition': `inline; filename=order.pdf`,
    });

    res.send(content);
    // return new StreamableFile(content);
  }

  @Post('/order/:id/post-pdf')
  // @Header('Content-Type', 'application/pdf')
  // @Header('Content-Disposition', 'attachment; filename=order.pdf')
  async reportOrderPostPdf(
    @Param('id') orderId: string,
    @Body() body: any,
    @Res() res: Response
  ) {
    const content = await this.reportService.reportOrderPostPdf1(+orderId, body);

    // const file = Readable.from(content);
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename=order.pdf`,
      // 'Content-Disposition': `inline; filename=order.pdf`,
    });

    res.send(content);
    // return new StreamableFile(content);
  }

  @Post('/order/:id/order-fake-pdf')
  // @Header('Content-Type', 'application/pdf')
  // @Header('Content-Disposition', 'attachment; filename=order.pdf')
  async reportOrderFakePdf(
    @Param('id') orderId: string,
    @Res() res: Response
  ) {
    const content = await this.reportService.reportOrderFakePdf1(+orderId);

    // const file = Readable.from(content);
    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename=order.pdf`,
      // 'Content-Disposition': `inline; filename=order.pdf`,
    });

    res.send(content);
    // return new StreamableFile(content);
  }

  // @Get('/report/order')
  // async report(@Res({ passthrough: true }) res: Response): Promise<StreamableFile> {
  //   // const file = Readable.from(await this.reportService.reportOrder());

  //   const filename = new Date().toLocaleDateString();

  //   res.set({
  //     'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //     'Content-Disposition': `attachment; filename="report-order.xlsx"`,
  //   });
  //   return new StreamableFile(file);
  // }

  // @Post()
  // create(@Body() createReportDto: CreateReportDto) {
  //   return this.reportService.create(createReportDto);
  // }

  // @Get()
  // findAll() {
  //   return this.reportService.findAll();
  // }

  // @Get(':id')
  // findOne(@Param('id') id: string) {
  //   return this.reportService.findOne(+id);
  // }

  // @Patch(':id')
  // update(@Param('id') id: string, @Body() updateReportDto: UpdateReportDto) {
  //   return this.reportService.update(+id, updateReportDto);
  // }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.reportService.remove(+id);
  // }
  @Post('Reward-report')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        startDate: { type: 'string', format: 'date', example: '2024-01-01' },
        endDate: { type: 'string', format: 'date', example: '2024-12-31' },
        rewardCode: { type: 'array', items: { type: 'string' }, example: ['R001'] }
      }
    }
  })
  async exportRewardReport(
    @Body('startDate') startDate: string,
    @Body('endDate') endDate: string,
    @Body('rewardCode') rewardCode: string[],
    @Res() res: Response
  ): Promise<void> {
    const start = DateTime.fromISO(startDate, { zone: 'UTC+7' }).startOf('day').toJSDate();
    const end = DateTime.fromISO(endDate, { zone: 'UTC+7' }).endOf('day').toJSDate();
    const reportBuffer = await this.reportService.exportRewardReport(start, end, rewardCode);

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=reward_report.xlsx');

    res.send(reportBuffer);
  }

  //รายงานสินค้าคงเหลือแยกตามคลัง
  @Get('/stock-report')
  @ApiOperation({ summary: 'รายงานสินค้าคงเหลือแยกตามคลัง' })
  async stockReport(
    @Query('productId') productId?: number,
    @Query('categoryId') categoryId?: number,
  ) {
    return this.reportService.stockReport(productId, categoryId);
  }

  //รายงานสรุปยอดซื้อวัสดุรายชนิด
  @Get('/purchase-report')
  @ApiOperation({ summary: 'รายงานสรุปยอดซื้อวัสดุรายชนิด' })
  @ApiQuery({ name: 'startDate', description: 'วันที่เริ่ม', type: String, example: '2025-05-01' })
  @ApiQuery({ name: 'endDate', description: 'วันที่สิ้นสุด', type: String, example: '2025-05-30' })
  @ApiQuery({ name: 'categoryId', description: 'หมวดหมู่สินค้า', type: Number, example: 1, required: false })
  async purchaseReport(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('categoryId') categoryId?: number,
  ) {
    const start = DateTime.fromISO(startDate, { zone: 'UTC+7' }).startOf('day').toJSDate();
    const end = DateTime.fromISO(endDate, { zone: 'UTC+7' }).endOf('day').toJSDate();

    return this.reportService.purchaseReport(start, end, categoryId);
  }

  //รายงานรายละเอียดการยอดซือวัสดุรายชนิด
  @Get('/purchase-detail-report')
  @ApiOperation({ summary: 'รายงานรายละเอียดการยอดซือวัสดุรายชนิด' })
  @ApiQuery({ name: 'startDate', description: '', type: String, example: '2025-05-01' })
  @ApiQuery({ name: 'endDate', description: '', type: String, example: '2025-05-30' })
  @ApiQuery({ name: 'categoryId', description: 'หมวด', type: Number, example: 1 })
  async purchaseDetailReport(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('categoryId') categoryId: number,
  ) {
    const start = DateTime.fromISO(startDate, { zone: 'UTC+7' }).startOf('day').toJSDate();
    const end = DateTime.fromISO(endDate, { zone: 'UTC+7' }).endOf('day').toJSDate();

    return this.reportService.purchaseDetailReport(start, end, categoryId);
  }
}
