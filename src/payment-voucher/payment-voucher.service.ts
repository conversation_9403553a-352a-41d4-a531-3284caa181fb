import { Injectable, NotFoundException, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { paginate, PaginateQuery, PaginateConfig } from 'nestjs-paginate';
import { PaymentVoucher, PaymentVoucherStatus } from './entities/payment-voucher.entity';
import { PaymentVoucherItem, PaymentVoucherItemType } from './entities/payment-voucher-item.entity';
import { CreatePaymentVoucherDto } from './dto/create-payment-voucher.dto';
import { UpdatePaymentVoucherDto } from './dto/update-payment-voucher.dto';
import { CreatePaymentVoucherFromGrDto } from './dto/create-payment-voucher-from-gr.dto';
import { GoodsReceiptService } from '../goods-receipt/goods-receipt.service';
import { GoodsReceipt } from '../goods-receipt/entities/goods-receipt.entity';

export const PAYMENT_VOUCHER_PAGINATION_CONFIG: PaginateConfig<PaymentVoucher> = {
  sortableColumns: ['id', 'pvNumber', 'pvDate', 'status', 'totalAmount', 'createdAt'],
  defaultSortBy: [['createdAt', 'DESC']],
  searchableColumns: ['pvNumber', 'notes', 'paymentReference'],
  relations: {
    supplier: true,
    branch: true,
    purchaseOrder: true,
    goodsReceipt: true,
    createdBy: true,
    approvedBy: true,
    paidBy: true,
    items: {
      purchaseOrderItem: {
        product: true
      },
      // goodsReceiptItem: {
      //   product: true
      // }
    }
  },
  filterableColumns: {
    status: true,
    paymentMethod: true,
    branchId: true,
    supplierId: true,
    purchaseOrderId: true,
    goodsReceiptId: true
  }
};

@Injectable()
export class PaymentVoucherService {
  constructor(
    @InjectRepository(PaymentVoucher)
    private readonly paymentVoucherRepository: Repository<PaymentVoucher>,
    @InjectRepository(PaymentVoucherItem)
    private readonly paymentVoucherItemRepository: Repository<PaymentVoucherItem>,
    @InjectRepository(GoodsReceipt)
    private readonly goodsReceiptRepository: Repository<GoodsReceipt>,
    @Inject(forwardRef(() => GoodsReceiptService))
    private readonly goodsReceiptService: GoodsReceiptService,
    private readonly dataSource: DataSource,
  ) {}

  async datatables(query: PaginateQuery) {
    return paginate(query, this.paymentVoucherRepository, PAYMENT_VOUCHER_PAGINATION_CONFIG);
  }

  async findAll() {
    return this.paymentVoucherRepository.find({
      relations: {
        supplier: true,
        branch: true,
        purchaseOrder: true,
        goodsReceipt: true,
        createdBy: true,
        approvedBy: true,
        paidBy: true,
        items: {
          purchaseOrderItem: {
            product: {
              category: true,
              unit: true
            }
          },
          // goodsReceiptItem: {
          //   product: {
          //     category: true,
          //     unit: true
          //   }
          // }
        }
      },
      order: {
        createdAt: 'DESC'
      }
    });
  }

  async findOne(id: number) {
    const paymentVoucher = await this.paymentVoucherRepository.findOne({
      where: { id },
      relations: {
        supplier: true,
        branch: true,
        purchaseOrder: {
          items: {
            product: true
          }
        },
        goodsReceipt: {
          items: {
            product: true
          }
        },
        createdBy: true,
        approvedBy: true,
        paidBy: true,
        items: {
          purchaseOrderItem: {
            product: {
              category: true,
              unit: true
            }
          },
          // goodsReceiptItem: {
          //   product: {
          //     category: true,
          //     unit: true
          //   }
          // }
        }
      }
    });

    if (!paymentVoucher) {
      throw new NotFoundException(`Payment Voucher with ID ${id} not found`);
    }

    return paymentVoucher;
  }

  async findByPvNumber(pvNumber: string) {
    const paymentVoucher = await this.paymentVoucherRepository.findOne({
      where: { pvNumber },
      relations: {
        supplier: true,
        branch: true,
        purchaseOrder: true,
        goodsReceipt: true,
        createdBy: true,
        approvedBy: true,
        paidBy: true,
        items: {
          purchaseOrderItem: {
            product: true
          },
          // goodsReceiptItem: {
          //   product: true
          // }
        }
      }
    });

    if (!paymentVoucher) {
      throw new NotFoundException(`Payment Voucher with PV Number ${pvNumber} not found`);
    }

    return paymentVoucher;
  }

  async create(createPaymentVoucherDto: CreatePaymentVoucherDto, userId: number) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // ตรวจสอบว่าเลขที่ PV ซ้ำหรือไม่
      const existingPv = await queryRunner.manager.findOne(PaymentVoucher, {
        where: { pvNumber: createPaymentVoucherDto.pvNumber }
      });

      if (existingPv) {
        throw new BadRequestException(`Payment Voucher number ${createPaymentVoucherDto.pvNumber} already exists`);
      }

      // สร้าง PaymentVoucher
      const paymentVoucher = queryRunner.manager.create(PaymentVoucher, {
        ...createPaymentVoucherDto,
        pvDate: new Date(createPaymentVoucherDto.pvDate),
        paymentDate: createPaymentVoucherDto.paymentDate ? new Date(createPaymentVoucherDto.paymentDate) : null,
        approvedAt: createPaymentVoucherDto.approvedAt ? new Date(createPaymentVoucherDto.approvedAt) : null,
        createdBy: { id: userId } as any,
        supplier: { id: createPaymentVoucherDto.supplierId } as any,
        branch: { id: createPaymentVoucherDto.branchId } as any,
        purchaseOrder: createPaymentVoucherDto.purchaseOrderId ? { id: createPaymentVoucherDto.purchaseOrderId } as any : null,
        goodsReceipt: createPaymentVoucherDto.goodsReceiptId ? { id: createPaymentVoucherDto.goodsReceiptId } as any : null,
        approvedBy: createPaymentVoucherDto.approvedById ? { id: createPaymentVoucherDto.approvedById } as any : null,
        paidBy: createPaymentVoucherDto.paidById ? { id: createPaymentVoucherDto.paidById } as any : null
      });

      const savedPaymentVoucher = await queryRunner.manager.save(paymentVoucher);

      // สร้าง PaymentVoucherItems
      for (const itemDto of createPaymentVoucherDto.items) {
        const item = queryRunner.manager.create(PaymentVoucherItem, {
          ...itemDto,
          paymentVoucher: savedPaymentVoucher,
          purchaseOrderItem: itemDto.purchaseOrderItemId ? { id: itemDto.purchaseOrderItemId } as any : null,
          goodsReceiptItem: itemDto.goodsReceiptItemId ? { id: itemDto.goodsReceiptItemId } as any : null
        });

        await queryRunner.manager.save(item);
      }

      await queryRunner.commitTransaction();
      return this.findOne(savedPaymentVoucher.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async createFromGoodsReceipt(createDto: CreatePaymentVoucherFromGrDto, userId: number) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // ตรวจสอบ GR
      const goodsReceipt = await queryRunner.manager.findOne(GoodsReceipt, {
        where: { id: createDto.goodsReceiptId },
        relations: {
          items: {
            product: true
          },
          supplier: true,
          branch: true,
          purchaseOrder: true
        }
      });

      if (!goodsReceipt) {
        throw new NotFoundException(`Goods Receipt with ID ${createDto.goodsReceiptId} not found`);
      }

      // ตรวจสอบว่าเลขที่ PV ซ้ำหรือไม่
      const existingPv = await queryRunner.manager.findOne(PaymentVoucher, {
        where: { pvNumber: createDto.pvNumber }
      });

      if (existingPv) {
        throw new BadRequestException(`Payment Voucher number ${createDto.pvNumber} already exists`);
      }

      // สร้าง PaymentVoucher
      const paymentVoucher = queryRunner.manager.create(PaymentVoucher, {
        pvNumber: createDto.pvNumber,
        pvDate: new Date(createDto.pvDate),
        paymentDate: createDto.paymentDate ? new Date(createDto.paymentDate) : null,
        paymentMethod: createDto.paymentMethod,
        notes: createDto.notes,
        paymentReference: createDto.paymentReference,
        status: PaymentVoucherStatus.DRAFT,
        goodsReceipt: goodsReceipt,
        purchaseOrder: goodsReceipt.purchaseOrder,
        supplier: goodsReceipt.supplier,
        branch: goodsReceipt.branch,
        createdBy: { id: userId } as any,
        paidBy: createDto.paidById ? { id: createDto.paidById } as any : null,
        subtotal: 0,
        discountAmount: 0,
        taxAmount: 0,
        totalAmount: 0
      });

      const savedPaymentVoucher = await queryRunner.manager.save(paymentVoucher);

      let subtotal = 0;
      let totalDiscountAmount = 0;
      let totalTaxAmount = 0;

      // สร้าง PaymentVoucherItems จาก GR Items
      for (const paymentItem of createDto.items) {
        const grItem = goodsReceipt.items.find(item => item.id === paymentItem.goodsReceiptItemId);

        if (!grItem) {
          throw new NotFoundException(`Goods Receipt Item with ID ${paymentItem.goodsReceiptItemId} not found`);
        }

        // ตรวจสอบจำนวนที่จ่ายไม่เกินจำนวนที่รับ
        if (paymentItem.paymentQuantity > grItem.receivedQuantity) {
          throw new BadRequestException(`Payment quantity (${paymentItem.paymentQuantity}) exceeds received quantity (${grItem.receivedQuantity}) for product ${grItem.product.name}`);
        }

        const totalPrice = paymentItem.paymentQuantity * paymentItem.unitPrice;
        const discountAmount = paymentItem.discountAmount || 0;
        const taxAmount = paymentItem.taxAmount || 0;
        const netAmount = totalPrice - discountAmount + taxAmount;

        subtotal += totalPrice;
        totalDiscountAmount += discountAmount;
        totalTaxAmount += taxAmount;

        const pvItem = queryRunner.manager.create(PaymentVoucherItem, {
          paymentVoucher: savedPaymentVoucher,
          itemType: PaymentVoucherItemType.GOODS_RECEIPT,
          goodsReceiptItem: grItem,
          description: `${grItem.product.name} - ${grItem.product.code}`,
          quantity: paymentItem.paymentQuantity,
          unitPrice: paymentItem.unitPrice,
          totalPrice: totalPrice,
          discountAmount: discountAmount,
          taxAmount: taxAmount,
          netAmount: netAmount,
          notes: paymentItem.notes
        });

        await queryRunner.manager.save(pvItem);
      }

      const totalAmount = subtotal - totalDiscountAmount + totalTaxAmount;

      // อัปเดต total amounts
      await queryRunner.manager.update(PaymentVoucher, savedPaymentVoucher.id, {
        subtotal: subtotal,
        discountAmount: totalDiscountAmount,
        taxAmount: totalTaxAmount,
        totalAmount: totalAmount
      });

      await queryRunner.commitTransaction();
      return this.findOne(savedPaymentVoucher.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async update(id: number, updatePaymentVoucherDto: UpdatePaymentVoucherDto, userId: number) {
    const paymentVoucher = await this.findOne(id);

    if (paymentVoucher.status === PaymentVoucherStatus.PAID) {
      throw new BadRequestException('Cannot update paid payment voucher');
    }

    const updateData: any = { ...updatePaymentVoucherDto };

    // แปลงวันที่
    if (updateData.pvDate) {
      updateData.pvDate = new Date(updateData.pvDate);
    }
    if (updateData.paymentDate) {
      updateData.paymentDate = new Date(updateData.paymentDate);
    }
    if (updateData.approvedAt) {
      updateData.approvedAt = new Date(updateData.approvedAt);
    }

    // ลบ foreign key fields และ items ออกจาก updateData
    delete updateData.supplierId;
    delete updateData.branchId;
    delete updateData.purchaseOrderId;
    delete updateData.goodsReceiptId;
    delete updateData.approvedById;
    delete updateData.paidById;
    delete updateData.items;

    await this.paymentVoucherRepository.update(id, {
      ...updateData,
      lastUpdatedBy: userId
    });

    return this.findOne(id);
  }

  async approve(id: number, userId: number) {
    const paymentVoucher = await this.findOne(id);

    if (paymentVoucher.status !== PaymentVoucherStatus.PENDING) {
      throw new BadRequestException('Only pending payment vouchers can be approved');
    }

    await this.paymentVoucherRepository.update(id, {
      status: PaymentVoucherStatus.APPROVED,
      approvedBy: { id: userId } as any,
      approvedAt: new Date(),
      // lastUpdatedBy: userId
    });

    return this.findOne(id);
  }

  async markAsPaid(id: number, userId: number, paymentReference?: string, paymentDate?: string) {
    const paymentVoucher = await this.findOne(id);

    if (paymentVoucher.status !== PaymentVoucherStatus.APPROVED) {
      throw new BadRequestException('Only approved payment vouchers can be marked as paid');
    }

    await this.paymentVoucherRepository.update(id, {
      status: PaymentVoucherStatus.PAID,
      paidBy: { id: userId } as any,
      paymentDate: paymentDate ? new Date(paymentDate) : new Date(),
      paymentReference: paymentReference || paymentVoucher.paymentReference,
      // lastUpdatedBy: userId
    });

    return this.findOne(id);
  }

  async remove(id: number) {
    const paymentVoucher = await this.findOne(id);

    if (paymentVoucher.status === PaymentVoucherStatus.PAID) {
      throw new BadRequestException('Cannot delete paid payment voucher');
    }

    await this.paymentVoucherRepository.softDelete(id);
  }

  async generatePvNumber(): Promise<string> {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');

    const prefix = `PV${year}${month}`;

    // หาเลขที่ล่าสุดในเดือนนี้
    const lastPv = await this.paymentVoucherRepository
      .createQueryBuilder('pv')
      .where('pv.pvNumber LIKE :prefix', { prefix: `${prefix}%` })
      .orderBy('pv.pvNumber', 'DESC')
      .getOne();

    let nextNumber = 1;
    if (lastPv) {
      const lastNumber = parseInt(lastPv.pvNumber.substring(prefix.length));
      nextNumber = lastNumber + 1;
    }

    return `${prefix}${String(nextNumber).padStart(4, '0')}`;
  }
}
