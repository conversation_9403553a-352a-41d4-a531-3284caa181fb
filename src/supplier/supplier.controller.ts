import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  ParseIntPipe,
  Put,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { SupplierService } from './supplier.service';
import { CreateSupplierDto } from './dto/create-supplier.dto';
import { UpdateSupplierDto } from './dto/update-supplier.dto';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { FileInterceptor } from '@nestjs/platform-express';

@ApiTags('ผู้จำหน่าย (Suppliers)')
@Auth()
@Controller('suppliers')
export class SupplierController {
  constructor(private readonly supplierService: SupplierService) { }

  @Get('datatables')
  @ApiOperation({ summary: 'ดึงข้อมูลผู้จำหน่ายแบบ pagination' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  datatables(@Paginate() query: PaginateQuery) {
    return this.supplierService.datatables(query);
  }

  @Post('import')
  @ApiOperation({
    summary: `นำเข้าข้อมูลผู้จัดจำหน่าย`,
    description: `<h1>/uploads/static/import/supplier-template.xlsx</h1>`
  })
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary'
        },
      },
    },
  })
  async importEmployeev2(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    return this.supplierService.importExcel(file);
  }

  @Get()
  @ApiOperation({ summary: 'ดึงข้อมูลผู้จำหน่ายทั้งหมด' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  findAll() {
    return this.supplierService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'ดึงข้อมูลผู้จำหน่ายตาม ID' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบข้อมูล' })
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.supplierService.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: 'สร้างผู้จำหน่ายใหม่' })
  @ApiResponse({ status: 201, description: 'สร้างสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  create(@Body() createSupplierDto: CreateSupplierDto) {
    return this.supplierService.create(createSupplierDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'แก้ไขข้อมูลผู้จำหน่าย' })
  @ApiResponse({ status: 200, description: 'แก้ไขสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบข้อมูล' })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateSupplierDto: UpdateSupplierDto,
  ) {
    return this.supplierService.update(id, updateSupplierDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'ลบผู้จำหน่าย' })
  @ApiResponse({ status: 200, description: 'ลบสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบข้อมูล' })
  @ApiResponse({ status: 400, description: 'ไม่สามารถลบได้' })
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.supplierService.remove(id);
  }

  @Put(':id/toggle-active')
  @ApiOperation({ summary: 'เปลี่ยนสถานะการใช้งานผู้จำหน่าย' })
  @ApiResponse({ status: 200, description: 'เปลี่ยนสถานะสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบข้อมูล' })
  toggleActive(@Param('id', ParseIntPipe) id: number) {
    return this.supplierService.toggleActive(id);
  }
}
