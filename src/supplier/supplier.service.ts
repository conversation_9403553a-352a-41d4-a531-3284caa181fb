import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { PaginateConfig, PaginateQuery, paginate } from 'nestjs-paginate';
import { Supplier } from './entities/supplier.entity';
import { CreateSupplierDto } from './dto/create-supplier.dto';
import { UpdateSupplierDto } from './dto/update-supplier.dto';
import * as xlsx from 'xlsx'

const SUPPLIER_PAGINATION_CONFIG: PaginateConfig<Supplier> = {
  sortableColumns: ['id', 'code', 'name', 'createdAt'],
  nullSort: 'last',
  defaultSortBy: [['createdAt', 'DESC']],
  searchableColumns: ['code', 'name', 'contactPerson', 'phone', 'email'],
  select: [
    'id',
    'code',
    'name',
    'contactPerson',
    'phone',
    'email',
    'address',
    'taxId',
    'website',
    'notes',
    'isActive',
    'createdAt',
    'updatedAt'
  ],
  filterableColumns: {
    isActive: true,
  },
};

@Injectable()
export class SupplierService {
  constructor(
    private dataSource: DataSource,
    @InjectRepository(Supplier)
    private readonly supplierRepository: Repository<Supplier>,
  ) { }

  async datatables(query: PaginateQuery) {
    return paginate(query, this.supplierRepository, SUPPLIER_PAGINATION_CONFIG);
  }

  async findAll() {
    return this.supplierRepository.find({
      where: { isActive: true },
      order: {
        createdAt: 'DESC'
      }
    });
  }

  async findOne(id: number) {
    const supplier = await this.supplierRepository.findOne({
      where: { id },
      relations: {
        purchaseOrders: {
          items: {
            product: true
          }
        },
      }
    });

    if (!supplier) {
      throw new NotFoundException(`ไม่พบผู้จำหน่ายรหัส ${id}`);
    }

    return supplier;
  }

  async findByCode(code: string) {
    return this.supplierRepository.findOne({
      where: { code }
    });
  }

  async create(createSupplierDto: CreateSupplierDto) {
    // ตรวจสอบรหัสผู้จำหน่ายซ้ำ
    const existingSupplier = await this.findByCode(createSupplierDto.code);
    if (existingSupplier) {
      throw new BadRequestException(`รหัสผู้จำหน่าย ${createSupplierDto.code} มีอยู่แล้ว`);
    }

    const supplier = this.supplierRepository.create(createSupplierDto);
    return await this.supplierRepository.save(supplier);
  }

  async update(id: number, updateSupplierDto: UpdateSupplierDto) {
    const existingSupplier = await this.findOne(id);

    // ตรวจสอบรหัสผู้จำหน่ายซ้ำ (ถ้ามีการเปลี่ยนรหัส)
    if (updateSupplierDto.code && updateSupplierDto.code !== existingSupplier.code) {
      const duplicateSupplier = await this.findByCode(updateSupplierDto.code);
      if (duplicateSupplier) {
        throw new BadRequestException(`รหัสผู้จำหน่าย ${updateSupplierDto.code} มีอยู่แล้ว`);
      }
    }

    await this.supplierRepository.update(id, updateSupplierDto);
    return this.findOne(id);
  }

  async remove(id: number) {
    const existingSupplier = await this.findOne(id);

    // ตรวจสอบว่ามี PurchaseOrder ที่เชื่อมโยงอยู่หรือไม่
    if (existingSupplier.purchaseOrders && existingSupplier.purchaseOrders.length > 0) {
      throw new BadRequestException('ไม่สามารถลบผู้จำหน่ายที่มีใบสั่งซื้อเชื่อมโยงอยู่');
    }

    return await this.supplierRepository.softDelete(id);
  }

  async toggleActive(id: number) {
    const supplier = await this.findOne(id);
    await this.supplierRepository.update(id, { isActive: !supplier.isActive });
    return this.findOne(id);
  }

  async importExcel(file: Express.Multer.File) {
    const HEADERS = [
      'รหัสผู้จำหน่าย (จำเป็น)', 'ชื่อผู้จำหน่าย (จำเป็น)', 'ชื่อผู้ติดต่อ', 'หมายเลขโทรศัพท์', 'อีเมล',
      'ที่อยู่', 'เลขประจำตัวผู้เสียภาษี', 'เว็บไซต์', 'หมายเหตุ'
    ];

    const workbook = xlsx.read(file.buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];
    const jsonData = xlsx.utils.sheet_to_json(sheet, { header: 1, defval: '' });

    const actualHeaders: string[] = jsonData[0] as Array<string>;

    // Validate headers
    const isValid = HEADERS.every(header => actualHeaders.includes(header));
    if (!isValid) {
      throw new BadRequestException('Header validation failed.');
    }

    const result = {
      ok: 0,
      error: 0,
      errorDetails: []
    };

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      for (let rowIndex = 1; rowIndex < jsonData.length; rowIndex++) {
        const row = jsonData[rowIndex];

        try {
          const code = row[actualHeaders.indexOf('รหัสผู้จำหน่าย (จำเป็น)')];
          const name = row[actualHeaders.indexOf('ชื่อผู้จำหน่าย (จำเป็น)')];
          const contactPerson = row[actualHeaders.indexOf('ชื่อผู้ติดต่อ')];
          const phone = row[actualHeaders.indexOf('หมายเลขโทรศัพท์')];
          const email = row[actualHeaders.indexOf('อีเมล')];
          const address = row[actualHeaders.indexOf('ที่อยู่')];
          const taxId = row[actualHeaders.indexOf('เลขประจำตัวผู้เสียภาษี')];
          const website = row[actualHeaders.indexOf('เว็บไซต์')];
          const notes = row[actualHeaders.indexOf('หมายเหตุ')];

          const existingSupplier = await queryRunner.manager.findOneBy(Supplier, { code });
          if (existingSupplier) {
            throw new Error('code already exists');
          }

          const supplier = queryRunner.manager.create(Supplier, {
            code,
            name,
            contactPerson,
            phone,
            email,
            address,
            taxId,
            website,
            notes,
          });

          await queryRunner.manager.save(supplier);

          result.ok += 1;
        } catch (error) {
          console.error(error);
          result.error += 1;
          result.errorDetails.push({
            row: rowIndex + 1,
            error: `on row ${rowIndex + 1}, ${error.message}`
          });
        }
      }

      await queryRunner.commitTransaction();
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw new BadRequestException(err?.message);
    } finally {
      await queryRunner.release();
      return result;
    }
  }
}
