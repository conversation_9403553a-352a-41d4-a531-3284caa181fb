import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DeliveryNoteService } from './delivery-note.service';
import { DeliveryNoteController } from './delivery-note.controller';
import { DeliveryNote } from './entities/delivery-note.entity';
import { DeliveryNoteItem } from './entities/delivery-note-item.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DeliveryNote,
      DeliveryNoteItem
    ]),
  ],
  controllers: [DeliveryNoteController],
  providers: [DeliveryNoteService],
  exports: [DeliveryNoteService],
})
export class DeliveryNoteModule {}
