import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsDateString, IsBoolean, Min } from 'class-validator';

export class CreateDeliveryNoteItemDto {
    @ApiProperty({ description: 'รหัสรายการในใบขาย', required: false })
    @IsOptional()
    @IsNumber()
    saleItemId?: number;

    @ApiProperty({ description: 'รหัสสินค้า' })
    @IsNumber()
    @IsNotEmpty()
    productId: number;

    // @ApiProperty({ description: 'จำนวนที่สั่ง' })
    // @IsNumber()
    // @IsNotEmpty()
    // @Min(0)
    // orderedQuantity: number;

    @ApiProperty({ description: 'จำนวนที่ส่งจริง' })
    @IsNumber()
    @IsNotEmpty()
    @Min(0)
    deliveredQuantity: number;

    @ApiProperty({ description: 'น้ำหนักต่อหน่วย', required: false })
    @IsOptional()
    @IsNumber()
    @Min(0)
    unitWeight?: number;

    @ApiProperty({ description: 'น้ำหนักรวม', required: false })
    @IsOptional()
    @IsNumber()
    @Min(0)
    totalWeight?: number;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;

    // @ApiProperty({ description: 'สภาพสินค้า', required: false })
    // @IsOptional()
    // @IsString()
    // condition?: string;

    // @ApiProperty({ description: 'หมายเลข lot/batch', required: false })
    // @IsOptional()
    // @IsString()
    // batchNumber?: string;

    // @ApiProperty({ description: 'วันหมดอายุ', required: false })
    // @IsOptional()
    // @IsDateString()
    // expiryDate?: string;

    // @ApiProperty({ description: 'สินค้าเสียหาย', default: false })
    // @IsOptional()
    // @IsBoolean()
    // isDamaged?: boolean;

    // @ApiProperty({ description: 'รายละเอียดความเสียหาย', required: false })
    // @IsOptional()
    // @IsString()
    // damageDescription?: string;
}
