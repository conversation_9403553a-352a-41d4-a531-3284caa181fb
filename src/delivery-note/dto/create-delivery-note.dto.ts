import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsDateString, IsArray, ValidateNested, IsEnum, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateDeliveryNoteItemDto } from './create-delivery-note-item.dto';
import { DeliveryNoteStatus } from '../entities/delivery-note.entity';

export class CreateDeliveryNoteDto {
    @ApiProperty({ description: 'เลขที่ใบส่งของ' })
    @IsString()
    @IsNotEmpty()
    deliveryNoteNumber: string;

    @ApiProperty({ description: 'วันที่ส่งของ' })
    @IsDateString()
    deliveryDate: string;

    @ApiProperty({ description: 'วันที่คาดว่าจะส่งถึง', required: false })
    @IsOptional()
    @IsDateString()
    expectedDeliveryDate?: string;

    @ApiProperty({ description: 'วันที่ส่งถึงจริง', required: false })
    @IsOptional()
    @IsDateString()
    actualDeliveryDate?: string;

    @ApiProperty({ description: 'สถานะ', enum: DeliveryNoteStatus, default: DeliveryNoteStatus.DRAFT })
    @IsOptional()
    @IsEnum(DeliveryNoteStatus)
    status?: DeliveryNoteStatus;

    @ApiProperty({ description: 'รหัสใบขาย' })
    @IsNumber()
    @IsNotEmpty()
    saleId: number;

    @ApiProperty({ description: 'รหัสลูกค้า' })
    @IsNumber()
    @IsNotEmpty()
    customerId: number;

    @ApiProperty({ description: 'รหัสสาขา' })
    @IsNumber()
    @IsNotEmpty()
    branchId: number;

    @ApiProperty({ description: 'รหัสคลังสินค้า' })
    @IsNumber()
    @IsNotEmpty()
    warehouseId: number;

    @ApiProperty({ description: 'ที่อยู่จัดส่ง', required: false })
    @IsOptional()
    @IsString()
    deliveryAddress?: string;

    @ApiProperty({ description: 'ผู้ติดต่อ', required: false })
    @IsOptional()
    @IsString()
    contactPerson?: string;

    @ApiProperty({ description: 'เบอร์โทรติดต่อ', required: false })
    @IsOptional()
    @IsString()
    contactPhone?: string;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiProperty({ description: 'คำแนะนำการจัดส่ง', required: false })
    @IsOptional()
    @IsString()
    deliveryInstructions?: string;

    @ApiProperty({ description: 'รหัสผู้ส่งของ', required: false })
    @IsOptional()
    @IsNumber()
    deliveredById?: number;

    @ApiProperty({ description: 'วันที่ส่งของ', required: false })
    @IsOptional()
    @IsDateString()
    deliveredAt?: string;

    @ApiProperty({ description: 'รหัสผู้รับของ', required: false })
    @IsOptional()
    @IsNumber()
    receivedById?: number;

    @ApiProperty({ description: 'วันที่รับของ', required: false })
    @IsOptional()
    @IsDateString()
    receivedAt?: string;

    @ApiProperty({ description: 'ชื่อผู้รับ', required: false })
    @IsOptional()
    @IsString()
    receiverName?: string;

    @ApiProperty({ description: 'ลายเซ็นผู้รับ', required: false })
    @IsOptional()
    @IsString()
    receiverSignature?: string;

    @ApiProperty({ description: 'จำนวนรวมทั้งหมด', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    totalQuantity?: number;

    @ApiProperty({ description: 'น้ำหนักรวม', default: 0 })
    @IsOptional()
    @IsNumber()
    @Min(0)
    totalWeight?: number;

    @ApiProperty({ description: 'ทะเบียนรถ', required: false })
    @IsOptional()
    @IsString()
    vehicleNumber?: string;

    @ApiProperty({ description: 'ชื่อคนขับ', required: false })
    @IsOptional()
    @IsString()
    driverName?: string;

    @ApiProperty({ description: 'เบอร์โทรคนขับ', required: false })
    @IsOptional()
    @IsString()
    driverPhone?: string;

    @ApiProperty({ description: 'รายการสินค้า', type: [CreateDeliveryNoteItemDto] })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => CreateDeliveryNoteItemDto)
    items: CreateDeliveryNoteItemDto[];
}
