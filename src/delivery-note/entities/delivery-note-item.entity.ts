import { Entity, Column, ManyToOne } from 'typeorm';
import { CustomBaseEntity } from '../../common/entities/custom-base.entity';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';
import { DeliveryNote } from './delivery-note.entity';
import { Product } from '../../product/entities/product.entity';
import { SaleItem } from '../../sale/entities/sale-item.entity';

@Entity()
export class DeliveryNoteItem extends CustomBaseEntity {
    @ManyToOne(() => DeliveryNote, (deliveryNote) => deliveryNote.items, { onDelete: 'CASCADE' })
    deliveryNote: DeliveryNote;

    @ManyToOne(() => SaleItem, (saleItem) => saleItem.deliveryNoteItems, { nullable: true })
    saleItem: SaleItem; // อ้างอิงจากรายการในใบขาย

    @ManyToOne(() => Product, (product) => product.deliveryNoteItems)
    product: Product;

    @Column()
    productName: string; // ชื่อสินค้า (เก็บไว้เผื่อสินค้าถูกลบ)

    @Column()
    productCode: string; // รหัสสินค้า (เก็บไว้เผื่อสินค้าถูกลบ)

    // @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    // orderedQuantity: number; // จำนวนที่สั่ง (จากใบขาย)

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    deliveredQuantity: number; // จำนวนที่ส่งจริง

    @Column({ nullable: true })
    unitName: string; // ชื่อหน่วย

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), nullable: true })
    unitWeight: number; // น้ำหนักต่อหน่วย

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), nullable: true })
    totalWeight: number; // น้ำหนักรวม

    @Column({ type: 'text', nullable: true })
    notes: string; // หมายเหตุ

    // @Column({ type: 'text', nullable: true })
    // condition: string; // สภาพสินค้า

    // @Column({ nullable: true })
    // batchNumber: string; // หมายเลข lot/batch

    // @Column({ nullable: true })
    // expiryDate: Date; // วันหมดอายุ

    // @Column({ type: 'boolean', default: false })
    // isDamaged: boolean; // สินค้าเสียหาย

    // @Column({ type: 'text', nullable: true })
    // damageDescription: string; // รายละเอียดความเสียหาย
}
