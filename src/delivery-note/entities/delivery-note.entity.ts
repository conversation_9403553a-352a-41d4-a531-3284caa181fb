import { 
    Entity, 
    Column, 
    ManyToOne, 
    OneToMany, 
    Index 
} from 'typeorm';
import { CustomBaseEntity } from '../../common/entities/custom-base.entity';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';
import { Customer } from '../../customer/entities/customer.entity';
import { Branch } from '../../branch/entities/branch.entity';
import { Warehouse } from '../../warehouse/entities/warehouse.entity';
import { User } from '../../user/entities/user.entity';
import { Sale } from '../../sale/entities/sale.entity';
import { DeliveryNoteItem } from './delivery-note-item.entity';

export enum DeliveryNoteStatus {
    DRAFT = 'draft',
    PENDING = 'pending',
    IN_TRANSIT = 'in_transit',
    DELIVERED = 'delivered',
    CANCELLED = 'cancelled'
}

@Entity()
export class DeliveryNote extends CustomBaseEntity {
    @Index()
    @Column({ unique: true })
    deliveryNoteNumber: string; // เลขที่ใบส่งของ

    @Column()
    deliveryDate: Date; // วันที่ส่งของ

    @Column({ nullable: true })
    expectedDeliveryDate: Date; // วันที่คาดว่าจะส่งถึง

    @Column({ nullable: true })
    actualDeliveryDate: Date; // วันที่ส่งถึงจริง

    @Column({ type: 'enum', enum: DeliveryNoteStatus, default: DeliveryNoteStatus.DRAFT })
    status: DeliveryNoteStatus;

    @ManyToOne(() => Sale, (sale) => sale.deliveryNotes)
    sale: Sale; // อ้างอิงจากใบขาย

    @ManyToOne(() => Customer, (customer) => customer.deliveryNotes)
    customer: Customer;

    @ManyToOne(() => Branch, (branch) => branch.deliveryNotes)
    branch: Branch;

    @ManyToOne(() => Warehouse, (warehouse) => warehouse.deliveryNotes)
    warehouse: Warehouse;

    @Column({ type: 'text', nullable: true })
    deliveryAddress: string; // ที่อยู่จัดส่ง

    @Column({ nullable: true })
    contactPerson: string; // ผู้ติดต่อ

    @Column({ nullable: true })
    contactPhone: string; // เบอร์โทรติดต่อ

    @Column({ type: 'text', nullable: true })
    notes: string; // หมายเหตุ

    @Column({ type: 'text', nullable: true })
    deliveryInstructions: string; // คำแนะนำการจัดส่ง

    @ManyToOne(() => User, (user) => user.deliveryNotes)
    createdBy: User; // ผู้สร้างใบส่งของ

    @ManyToOne(() => User, { nullable: true })
    deliveredBy: User; // ผู้ส่งของ

    @Column({ nullable: true })
    deliveredAt: Date; // วันที่ส่งของ

    @ManyToOne(() => User, { nullable: true })
    receivedBy: User; // ผู้รับของ (ถ้ามี)

    @Column({ nullable: true })
    receivedAt: Date; // วันที่รับของ

    @Column({ nullable: true })
    receiverName: string; // ชื่อผู้รับ (ถ้าไม่ใช่ user ในระบบ)

    @Column({ nullable: true })
    receiverSignature: string; // ลายเซ็นผู้รับ (path to image)

    @OneToMany(() => DeliveryNoteItem, (item) => item.deliveryNote, { cascade: true })
    items: DeliveryNoteItem[];

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    totalQuantity: number; // จำนวนรวมทั้งหมด

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    totalWeight: number; // น้ำหนักรวม (ถ้ามี)

    @Column({ nullable: true })
    vehicleNumber: string; // ทะเบียนรถ

    @Column({ nullable: true })
    driverName: string; // ชื่อคนขับ

    @Column({ nullable: true })
    driverPhone: string; // เบอร์โทรคนขับ
}
