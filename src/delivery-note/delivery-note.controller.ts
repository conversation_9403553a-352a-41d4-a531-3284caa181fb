import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Put, 
  Param, 
  Delete, 
  HttpCode, 
  HttpStatus, 
  ParseIntPipe, 
  Req,
  Patch,
  Query
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Request } from 'express';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { Auth } from '../auth/decorators/auth.decorator';
import { DeliveryNoteService, DELIVERY_NOTE_PAGINATION_CONFIG } from './delivery-note.service';
import { CreateDeliveryNoteDto } from './dto/create-delivery-note.dto';
import { UpdateDeliveryNoteDto } from './dto/update-delivery-note.dto';
import { DeliveryNoteStatus } from './entities/delivery-note.entity';

@Controller('delivery-note')
@ApiTags('ใบส่งของ (Delivery Note)')
@Auth()
export class DeliveryNoteController {
  constructor(private readonly deliveryNoteService: DeliveryNoteService) {}

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'ดึงข้อมูลใบส่งของแบบ pagination' })
  @ApiPaginationQuery(DELIVERY_NOTE_PAGINATION_CONFIG)
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  datatables(@Paginate() query: PaginateQuery) {
    return this.deliveryNoteService.datatables(query);
  }

  @Get('generate-delivery-note-number')
  @ApiOperation({ summary: 'สร้างเลขที่ใบส่งของอัตโนมัติ' })
  @ApiResponse({ status: 200, description: 'สร้างเลขที่สำเร็จ' })
  generateDeliveryNoteNumber() {
    return this.deliveryNoteService.generateDeliveryNoteNumber();
  }

  @Post('from-sale/:saleId')
  @ApiOperation({ summary: 'สร้างใบส่งของจากใบขาย' })
  @ApiParam({ name: 'saleId', description: 'รหัสใบขาย' })
  @ApiResponse({ status: 201, description: 'สร้างใบส่งของสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบขาย' })
  createFromSale(@Param('saleId', ParseIntPipe) saleId: number, @Req() req: Request) {
    const userId = req.user['sub'];
    return this.deliveryNoteService.createFromSale(saleId, userId);
  }

  @Post()
  @ApiOperation({ summary: 'สร้างใบส่งของใหม่' })
  @ApiResponse({ status: 201, description: 'สร้างใบส่งของสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  create(@Req() req: Request, @Body() createDeliveryNoteDto: CreateDeliveryNoteDto) {
    const userId = req.user['sub'];
    return this.deliveryNoteService.create(createDeliveryNoteDto, userId);
  }

  @Get()
  @ApiOperation({ summary: 'ดึงข้อมูลใบส่งของทั้งหมด' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  findAll() {
    return this.deliveryNoteService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'ดึงข้อมูลใบส่งของตาม ID' })
  @ApiParam({ name: 'id', description: 'รหัสใบส่งของ' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบส่งของ' })
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.deliveryNoteService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'อัปเดตใบส่งของ' })
  @ApiParam({ name: 'id', description: 'รหัสใบส่งของ' })
  @ApiResponse({ status: 200, description: 'อัปเดตสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบส่งของ' })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  update(
    @Param('id', ParseIntPipe) id: number, 
    @Req() req: Request,
    @Body() updateDeliveryNoteDto: UpdateDeliveryNoteDto
  ) {
    const userId = req.user['sub'];
    return this.deliveryNoteService.update(id, updateDeliveryNoteDto, userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'ลบใบส่งของ' })
  @ApiParam({ name: 'id', description: 'รหัสใบส่งของ' })
  @ApiResponse({ status: 200, description: 'ลบสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบส่งของ' })
  @ApiResponse({ status: 400, description: 'ไม่สามารถลบได้' })
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.deliveryNoteService.remove(id);
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'อัปเดตสถานะใบส่งของ' })
  @ApiParam({ name: 'id', description: 'รหัสใบส่งของ' })
  @ApiQuery({ name: 'status', enum: DeliveryNoteStatus, description: 'สถานะใหม่' })
  @ApiResponse({ status: 200, description: 'อัปเดตสถานะสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบส่งของ' })
  updateStatus(
    @Param('id', ParseIntPipe) id: number, 
    @Query('status') status: DeliveryNoteStatus,
    @Req() req: Request
  ) {
    const userId = req.user['sub'];
    return this.deliveryNoteService.updateStatus(id, status, userId);
  }

  @Patch(':id/delivered')
  @ApiOperation({ summary: 'ทำเครื่องหมายใบส่งของเป็นส่งแล้ว' })
  @ApiParam({ name: 'id', description: 'รหัสใบส่งของ' })
  @ApiResponse({ status: 200, description: 'อัปเดตสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบส่งของ' })
  markAsDelivered(
    @Param('id', ParseIntPipe) id: number, 
    @Req() req: Request,
    @Body() body: { receiverName?: string; receiverSignature?: string }
  ) {
    const userId = req.user['sub'];
    return this.deliveryNoteService.markAsDelivered(id, userId, body.receiverName, body.receiverSignature);
  }
}
