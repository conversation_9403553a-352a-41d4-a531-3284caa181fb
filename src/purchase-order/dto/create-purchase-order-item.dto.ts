import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, <PERSON>N<PERSON>ber, <PERSON>Optional, IsString, Min } from "class-validator";

export class CreatePurchaseOrderItemDto {
    @ApiProperty({ description: 'รหัสสินค้า' })
    @IsNotEmpty()
    @IsNumber()
    productId: number;

    // @ApiProperty({ description: 'ชื่อสินค้า' })
    // @IsNotEmpty()
    // @IsString()
    // productName: string;

    // @ApiProperty({ description: 'รหัสสินค้า' })
    // @IsNotEmpty()
    // @IsString()
    // productCode: string;

    @ApiProperty({ description: 'จำนวน' })
    @IsNotEmpty()
    @IsNumber()
    @Min(0.01, { message: 'จำนวนต้องมากกว่า 0' })
    quantity: number;

    @ApiProperty({ description: 'ราคาต่อหน่วย' })
    @IsNotEmpty()
    @IsNumber()
    @Min(0, { message: 'ราคาต้องมากกว่าหรือเท่ากับ 0' })
    unitPrice: number;

    @ApiProperty({ description: 'ราคารวม' })
    @IsNotEmpty()
    @IsNumber()
    @Min(0, { message: 'ราคารวมต้องมากกว่าหรือเท่ากับ 0' })
    totalPrice: number;

    @ApiProperty({ description: 'หมายเหตุ', required: false })
    @IsOptional()
    @IsString()
    notes?: string;
}
