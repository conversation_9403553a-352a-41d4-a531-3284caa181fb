import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { Category } from './entities/category.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';
import * as xlsx from 'xlsx'

export const CATEGORY_PAGINATION_CONFIG: PaginateConfig<Category> = {
    sortableColumns: ['id', 'name'],
    select: ['id', 'code', 'name', 'colorCode', 'createdAt'],
};
@Injectable()
export class CategoryService {
    constructor(
        private dataSource: DataSource,
        @InjectRepository(Category)
        private categoryRepository: Repository<Category>,
    ) { }

    create(createCategoryDto: CreateCategoryDto) {
        const category = this.categoryRepository.create(createCategoryDto);

        return this.categoryRepository.save(category);
    }

    findAll() {
        return this.categoryRepository.find({
            order: {
                code: "ASC",
            },
        });
    }

    async findOne(id: number) {
        const category = await this.categoryRepository.findOne({
            where: { id, }
        });
        if (!category) throw new NotFoundException("category not found");

        return category;
    }

    async update(id: number, updateCategoryDto: UpdateCategoryDto) {
        const category = await this.findById(id);

        if (!category) throw new NotFoundException("category not found");

        return this.categoryRepository.update(id, updateCategoryDto);
    }

    async remove(id: number) {
        const category = await this.findById(id);

        if (!category) throw new NotFoundException("category not found");

        category.code = new Date() + category.code
        await category.save()

        await this.categoryRepository.softRemove(category);
    }

    findById(id: number) {
        return this.categoryRepository.findOneBy({ id });
    }

    async datatables(query: PaginateQuery): Promise<Paginated<Category>> {
        return paginate(query, this.categoryRepository, CATEGORY_PAGINATION_CONFIG);
    }

    async importExcel(file: Express.Multer.File) {
        const HEADERS = [
            'code', 'name'
        ];

        const workbook = xlsx.read(file.buffer, { type: 'buffer' });
        const sheetName = workbook.SheetNames[0];
        const sheet = workbook.Sheets[sheetName];
        const jsonData = xlsx.utils.sheet_to_json(sheet, { header: 1, defval: '' });

        const actualHeaders: string[] = jsonData[0] as Array<string>;

        // Validate headers
        const isValid = HEADERS.every(header => actualHeaders.includes(header));
        if (!isValid) {
            throw new BadRequestException('Header validation failed.');
        }

        const result = {
            ok: 0,
            error: 0,
            errorDetails: []
        };

        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {

            for (let rowIndex = 1; rowIndex < jsonData.length; rowIndex++) {
                const row = jsonData[rowIndex];
                
                try {
                    const code = row[actualHeaders.indexOf('code')];
                    const name = row[actualHeaders.indexOf('name')];

                    const existingCategory = await queryRunner.manager.findOneBy(Category, { code });
                    if (existingCategory) {
                        throw new Error('code already exists');
                    }

                    const category = queryRunner.manager.create(Category, {
                        code,
                        name,
                    });
                    await queryRunner.manager.save(category);

                    result.ok += 1;
                } catch (error) {
                    console.error(error);
                    result.error += 1;
                    result.errorDetails.push({
                        row: rowIndex + 1,
                        error: `on row ${rowIndex + 1}, ${error.message}`
                    });
                }
            }

            await queryRunner.commitTransaction();
        } catch (err) {
            await queryRunner.rollbackTransaction();
            throw new BadRequestException(err?.message);
        } finally {
            await queryRunner.release();
            return result;
        }
    }
}
