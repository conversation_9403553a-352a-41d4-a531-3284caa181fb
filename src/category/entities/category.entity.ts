import { Product } from "../../product/entities/product.entity";
import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, Index, ManyToOne, OneToMany, Unique } from "typeorm";
import { Store } from "../../store/entities/store.entity";

@Entity()
@Unique(['code', 'store'])
export class Category extends CustomBaseEntity {
    @Column()
    code: string;

    @Column()
    name: string;

    @Column({ name: 'color_code', nullable: true })
    colorCode: string;

    @OneToMany(() => Product, (_) => _.category)
    products: Array<Product>;

    @ManyToOne(() => Store, (_) => _.categories)
    store: Store;

    constructor(partial?: Partial<Category>) {
        super();
        if (partial) {
            Object.assign(this, partial)
        }
    }
}
