import { Controller, Get, Post, Body, Put, Param, Delete, HttpCode, HttpStatus, BadRequestException, UploadedFile, UseInterceptors } from '@nestjs/common';
import { CATEGORY_PAGINATION_CONFIG, CategoryService } from './category.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { ApiBody, ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('category')
@ApiTags('หมวดหมู่')
@Auth()
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(CATEGORY_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.categoryService.datatables(query);
  }

  @Post('import')
  @ApiOperation({ 
    summary: `นำเข้าข้อมูลหมวดหมู่สินค้า`,
    description: `นำเข้าข้อมูลหมวดหมู่สินค้า จากไฟล์ Excel ที่มีรูปแบบตามที่กำหนดไว้ในหน้าแสดงรายละเอียดข้อมูลหมวดหมู่สินค้า
      <h1>/uploads/static/import/categary-template.xlsx</h1>
    `
  })
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary'
        },
      },
    },
  })
  async importEmployeev2(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    return this.categoryService.importExcel(file);
  }
  
  @Post()
  create(@Body() createCategoryDto: CreateCategoryDto) {
    return this.categoryService.create(createCategoryDto);
  }

  @Get()
  findAll() {
    return this.categoryService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.categoryService.findOne(+id);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateCategoryDto: UpdateCategoryDto) {
    return this.categoryService.update(+id, updateCategoryDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.categoryService.remove(+id);
  }
}
