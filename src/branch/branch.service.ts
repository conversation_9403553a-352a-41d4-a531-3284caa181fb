import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateBranchDto } from './dto/create-branch.dto';
import { UpdateBranchDto } from './dto/update-branch.dto';
import { Branch } from './entities/branch.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';

export const BRANCH_PAGINATION_CONFIG: PaginateConfig<Branch> = {
  sortableColumns: ['id', 'name'],
  select: ['id', 'code', 'name', 'createdAt'],
  filterableColumns: {
    'store.id': true,
  },
  relations: {
    store: true,
  }
};

@Injectable()
export class BranchService {
  constructor(
    @InjectRepository(Branch)
    private branchRepository: Repository<Branch>,
  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<Branch>> {
    return paginate(query, this.branchRepository, BRANCH_PAGINATION_CONFIG);
  }

  create(createBranchDto: CreateBranchDto) {
    const { storeId, ...data } = createBranchDto;

    const branch = this.branchRepository.create(
      {
        ...data,
        store: { id: storeId }
      });

    return this.branchRepository.save(branch);
  }

  findAll(shopId?: number) {
    if (!shopId) {
      return this.branchRepository.find();
    }

    return this.branchRepository.find({
      where: {
        store: {
          id: shopId
        }
      }
    });
  }

  async findOne(id: number) {
    const branch = await this.branchRepository.findOne({ where: { id } });

    if (!branch) throw new NotFoundException("branch not found");

    return branch;
  }

  async update(id: number, updateBranchDto: UpdateBranchDto) {
    const branch = this.findOneById(id);
    if (!branch) throw new NotFoundException("branch not found");

    const { storeId, ...data } = updateBranchDto;

    return this.branchRepository.update(id, {
      ...data,
      store: {
        id: storeId,
      }
    });
  }

  async remove(id: number) {
    const branch = this.findOneById(id);
    if (!branch) throw new NotFoundException("branch not found");

    await this.branchRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.branchRepository.findOne({ where: { id } });
  }
}
