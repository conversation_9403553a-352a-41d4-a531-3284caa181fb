import { Column, Entity, Index, OneToMany, ManyToMany, Unique } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Branch } from "../../branch/entities/branch.entity";
import { User } from "../../user/entities/user.entity";
import { Expose } from "class-transformer";
import { Category } from "../../category/entities/category.entity";
import { Product } from "../../product/entities/product.entity";

@Entity()
@Unique(['code'])
export class Store extends CustomBaseEntity {
    @Column()
    @Index({ unique: true })
    code: string;

    @Column()
    name: string;

    @Column({ nullable: true })
    address: string;

    @OneToMany(() => Branch, (_) => _.store)
    branchs: Array<Branch>;

    @OneToMany(() => Category, (_) => _.store)
    categories: Array<Category>;

    @OneToMany(() => Product, (_) => _.store)
    products: Array<Product>;

    @ManyToMany(() => User, (user) => user.stores)
    users: User[];

    @Column({ nullable: true, length: 13 })
    taxId: string;

    @Column({ nullable: true })
    logo: string;

    @Expose()
    get logoUrl(): string {
        if (!this.logo) return null;

        // ถ้า path ขึ้นต้นด้วย http:// หรือ https:// ให้ return path เลย
        // if (this.path.startsWith('http://') || this.path.startsWith('https://')) {
        //     return this.path;
        // }

        // ถ้าไม่ใช่ URL สมบูรณ์ ให้ต่อ base URL เข้าไป
        return `${process.env.APP_URL}/${this.logo}`;
    }

    constructor(partial?: Partial<Store>) {
        super();
        if (partial) {
            Object.assign(this, partial)
        }
    }
}
