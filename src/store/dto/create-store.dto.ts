import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";

export class CreateStoreDto {
    @IsNotEmpty()
    @ApiProperty()
    readonly code: string;
    
    @IsNotEmpty()
    @ApiProperty()
    readonly name: string;
    
    @IsNotEmpty()
    @ApiProperty()
    readonly address: string;

    @ApiProperty({ required: false })
    readonly taxId?: string;

    @ApiProperty({ required: false })
    readonly logo?: string;
}
