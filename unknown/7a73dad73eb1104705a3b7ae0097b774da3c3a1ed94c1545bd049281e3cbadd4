import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { PermissionGroup } from './entities/permission-group.entity';
import { Permission, PermissionType } from './entities/permission.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class PermissionService implements OnApplicationBootstrap {
  private readonly logger = new Logger(PermissionService.name);

  constructor(
    @InjectRepository(Permission)
    private permissionRepository: Repository<Permission>,
    @InjectRepository(PermissionGroup)
    private permissionGroupRepository: Repository<PermissionGroup>,
  ) { }

  async onApplicationBootstrap() {
    // this.logger.verbose('เริ่มต้นการตรวจสอบและสร้าง inventory สำหรับ product ที่ยังไม่มี inventory...');
    
    try {
      for (const key in PermissionType) {
        const permission = await this.permissionRepository.existsBy({ name: key });
        if (!permission) {
          await this.permissionRepository.save({
            name: key,
            description: key,
          });
        }
      }
      // this.logger.verbose('การตรวจสอบและสร้าง inventory เสร็จสิ้น');
    } catch (error) {
      // this.logger.error('เกิดข้อผิดพลาดในการตรวจสอบและสร้าง inventory:', error);
    }
  }

  permissionGroup() {
    return PermissionGroup.find({
      relations: {
        permissions: true,
      },
    });
  }


}
