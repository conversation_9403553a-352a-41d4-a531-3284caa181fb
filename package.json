{"name": "gs-recycle-api", "version": "1.0.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node ./node_modules/typeorm/cli", "migration:run": "npm run typeorm migration:run -- -d ./src/config/typeorm.ts", "migration:generate": "npm run typeorm -- -d ./src/config/typeorm.ts migration:generate ./src/database/migrations/$npm_config_name", "migration:create": "npm run typeorm -- migration:create ./src/database/migrations/$npm_config_name", "migration:revert": "npm run typeorm -- -d ./src/config/typeorm.ts migration:revert", "seed": "ts-node ./node_modules/typeorm-extension/bin/cli.cjs seed:run -d ./src/config/typeorm.ts"}, "dependencies": {"@nestjs/axios": "^3.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.4.20", "@nestjs/serve-static": "^4.0.1", "@nestjs/swagger": "^7.3.0", "@nestjs/typeorm": "^10.0.2", "@nestjs/websockets": "^10.4.20", "@scalar/nestjs-api-reference": "^0.5.6", "argon2": "^0.31.2", "axios": "^1.6.8", "bahttext": "^2.3.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "exceljs": "^4.4.0", "fs-extra": "^11.2.0", "handlebars": "^4.7.8", "lodash": "^4.17.21", "luxon": "^3.4.4", "nestjs-i18n": "^10.5.1", "nestjs-paginate": "^8.6.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.11.3", "puppeteer": "^24.9.0", "randomstring": "^1.3.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "swagger-themes": "^1.4.3", "swagger-ui-express": "^5.0.0", "typeorm": "^0.3.20", "typeorm-extension": "^3.4.0", "typeorm-naming-strategies": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/fs-extra": "^11.0.4", "@types/handlebars": "^4.0.40", "@types/jest": "^29.5.2", "@types/lodash": "^4.17.20", "@types/luxon": "^3.4.2", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/passport-jwt": "^4.0.1", "@types/randomstring": "^1.1.12", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}